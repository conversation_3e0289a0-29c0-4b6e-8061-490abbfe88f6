# -*- coding: utf-8 -*-
"""Real time image detection.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1ZJm75Bv1Y_RNefMtgTUNiP3bxlSbUHwO
"""

import tensorflow as tf

from tensorflow.keras.applications.mobilenet_v2 import MobileNetV2, preprocess_input, decode_predictions

from tensorflow.keras.preprocessing import image
import numpy as np
import matplotlib.pyplot as plt

model = MobileNetV2(weights='imagenet')
img_path ='/content/babar.jpg'

image.load_img(img_path)
image

# to show the image
img = image.load_img(img_path, target_size=(224, 224))
image_array =image.img_to_array(img)
image_array = np.expand_dims(image_array, axis=0)

image_array.shape

image_array = preprocess_input(image_array)

predication = model.predict(image_array)

decoded_predications = decode_predictions(predication,top=3)[0]

decoded_predications

for i,(imagenet_id, label,score) in enumerate(decoded_predications):
  print(i,imagenet_id, label,score * 100)

def predict_image_class(img_path):
    # Load and preprocess the image
    img = image.load_img(img_path, target_size=(224, 224))
    img_array = image.img_to_array(img)
    img_array = np.expand_dims(img_array, axis=0)
    img_array = preprocess_input(img_array)

    # Predict the class of the image
    predictions = model.predict(img_array)

    # Decode the predictions into class names
    decoded_predictions = decode_predictions(predictions, top=5)[0]

    # Display the image
    plt.imshow(img)
    plt.axis('off')  # Hide axis
    plt.show()

    for i,(imagenet_id, label,score) in enumerate(decoded_predications):
         print(f"{i+1}.{label}: {score * 100:.2f}%")

img_path = '/content/tayyeb11.jpg'
predict_image_class(img_path)

