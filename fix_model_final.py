#!/usr/bin/env python3
"""
Final Fix for Laptop Price Predictor
This script runs the EXACT code from your laptop_price_predictor1.py to regenerate correct model files
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import pickle
from sklearn.model_selection import train_test_split
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import OneHotEncoder
from sklearn.metrics import r2_score, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

def main():
    print("🔄 Running EXACT code from your training script...")
    print("=" * 60)
    
    # Load data exactly as in your script
    df = pd.read_csv('laptop_data.csv')
    print(f"📊 Loaded data shape: {df.shape}")
    
    # Data preprocessing - EXACT copy from your script
    df.drop(columns=['Unnamed: 0'], inplace=True)
    
    df['Ram'] = df['Ram'].str.replace('GB', '')
    df['Weight'] = df['Weight'].str.replace('kg', '')
    
    df['Ram'] = df['Ram'].astype('int32')
    df['Weight'] = df['Weight'].astype('float32')
    
    # Touchscreen and IPS
    df['Touchscreen'] = df['ScreenResolution'].apply(lambda x: 1 if 'Touchscreen' in x else 0)
    df['Ips'] = df['ScreenResolution'].apply(lambda x: 1 if 'IPS' in x else 0)
    
    # Screen resolution processing
    new = df['ScreenResolution'].str.split('x', n=1, expand=True)
    df['X_res'] = new[0]
    df['Y_res'] = new[1]
    df['X_res'] = df['X_res'].str.replace(',', '').str.findall(r'(\d+\.?\d+)').apply(lambda x: x[0])
    df['X_res'] = df['X_res'].astype('int')
    df['Y_res'] = df['Y_res'].astype('int')
    
    # Calculate PPI
    df['ppi'] = (((df['X_res']**2) + (df['Y_res']**2))**0.5/df['Inches']).astype('float')
    
    # Drop screen resolution columns
    df.drop(columns=['ScreenResolution'], inplace=True)
    df.drop(columns=['Inches', 'X_res', 'Y_res'], inplace=True)
    
    # CPU processing
    df['Cpu Name'] = df['Cpu'].apply(lambda x: " ".join(x.split()[0:3]))
    
    def fetch_processor(text):
        if text == 'Intel Core i7' or text == 'Intel Core i5' or text == 'Intel Core i3':
            return text
        else:
            if text.split()[0] == 'Intel':
                return 'Other Intel Processor'
            else:
                return 'AMD Processor'
    
    df['Cpu brand'] = df['Cpu Name'].apply(fetch_processor)
    df.drop(columns=['Cpu', 'Cpu Name'], inplace=True)
    
    # Memory processing - EXACT copy
    df['Memory'] = df['Memory'].astype(str).replace('\.0', '', regex=True)
    df["Memory"] = df["Memory"].str.replace('GB', '')
    df["Memory"] = df["Memory"].str.replace('TB', '000')
    new = df["Memory"].str.split("+", n=1, expand=True)
    
    df["first"] = new[0]
    df["first"] = df["first"].str.strip()
    df["second"] = new[1]
    
    df["Layer1HDD"] = df["first"].apply(lambda x: 1 if "HDD" in x else 0)
    df["Layer1SSD"] = df["first"].apply(lambda x: 1 if "SSD" in x else 0)
    df["Layer1Hybrid"] = df["first"].apply(lambda x: 1 if "Hybrid" in x else 0)
    df["Layer1Flash_Storage"] = df["first"].apply(lambda x: 1 if "Flash Storage" in x else 0)
    
    df['first'] = df['first'].str.replace(r'\D', '', regex=True)
    df["second"] = df["second"].fillna("0").str.strip()
    
    df["Layer2HDD"] = df["second"].apply(lambda x: 1 if "HDD" in x else 0)
    df["Layer2SSD"] = df["second"].apply(lambda x: 1 if "SSD" in x else 0)
    df["Layer2Hybrid"] = df["second"].apply(lambda x: 1 if "Hybrid" in x else 0)
    df["Layer2Flash_Storage"] = df["second"].apply(lambda x: 1 if "Flash Storage" in x else 0)
    
    df['second'] = df['second'].str.replace(r'\D', '', regex=True)
    df["first"] = df["first"].astype(int)
    df["second"] = df["second"].astype(int)
    
    df["HDD"] = (df["first"]*df["Layer1HDD"]+df["second"]*df["Layer2HDD"])
    df["SSD"] = (df["first"]*df["Layer1SSD"]+df["second"]*df["Layer2SSD"])
    df["Hybrid"] = (df["first"]*df["Layer1Hybrid"]+df["second"]*df["Layer2Hybrid"])
    df["Flash_Storage"] = (df["first"]*df["Layer1Flash_Storage"]+df["second"]*df["Layer2Flash_Storage"])
    
    df.drop(columns=['first', 'second', 'Layer1HDD', 'Layer1SSD', 'Layer1Hybrid',
           'Layer1Flash_Storage', 'Layer2HDD', 'Layer2SSD', 'Layer2Hybrid',
           'Layer2Flash_Storage'], inplace=True)
    
    df.drop(columns=['Memory'], inplace=True)
    df.drop(columns=['Hybrid', 'Flash_Storage'], inplace=True)
    
    # GPU processing
    df['Gpu brand'] = df['Gpu'].apply(lambda x: x.split()[0])
    df = df[df['Gpu brand'] != 'ARM']
    df.drop(columns=['Gpu'], inplace=True)
    
    # OS processing
    def cat_os(inp):
        if inp == 'Windows 10' or inp == 'Windows 7' or inp == 'Windows 10 S':
            return 'Windows'
        elif inp == 'macOS' or inp == 'Mac OS X':
            return 'Mac'
        else:
            return 'Others/No OS/Linux'
    
    df['os'] = df['OpSys'].apply(cat_os)
    df.drop(columns=['OpSys'], inplace=True)
    
    print(f"✅ Data preprocessing complete. Final shape: {df.shape}")
    print(f"📋 Final columns: {df.columns.tolist()}")
    
    # Prepare features and target - EXACT copy
    X = df.drop(columns=['Price'])
    y = np.log(df['Price'])
    
    # Train-test split - EXACT copy
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.15, random_state=2)
    
    print(f"📊 Training data shape: {X_train.shape}")
    print(f"📊 X_train columns: {X_train.columns.tolist()}")
    
    # Create the EXACT Random Forest pipeline from your script
    step1 = ColumnTransformer(transformers=[
        ('col_tnf', OneHotEncoder(sparse_output=False, drop='first'), [0, 1, 7, 10, 11])
    ], remainder='passthrough')
    
    step2 = RandomForestRegressor(n_estimators=100,
                                  random_state=3,
                                  max_samples=0.5,
                                  max_features=0.75,
                                  max_depth=15)
    
    pipe = Pipeline([
        ('step1', step1),
        ('step2', step2)
    ])
    
    # Drop the 'Memory' column before fitting - EXACT copy
    # Note: Memory column should already be dropped, but keeping this for safety
    if 'Memory' in X_train.columns:
        X_train_processed = X_train.drop(columns=['Memory'])
        X_test_processed = X_test.drop(columns=['Memory'])
    else:
        X_train_processed = X_train
        X_test_processed = X_test
    
    print("🎯 Training RandomForestRegressor model...")
    pipe.fit(X_train_processed, y_train)
    
    # Make predictions and evaluate
    y_pred = pipe.predict(X_test_processed)
    r2 = r2_score(y_test, y_pred)
    mae = mean_absolute_error(y_test, y_pred)
    
    print(f"📈 Model Performance:")
    print(f"   R² Score: {r2:.4f}")
    print(f"   MAE: {mae:.4f}")
    print(f"   Model Type: {type(pipe.named_steps['step2'])}")
    
    # Save the model and data - EXACT copy from your script
    print("💾 Saving model files...")
    
    pickle.dump(df, open('df.pkl', 'wb'))
    pickle.dump(pipe, open('pipe.pkl', 'wb'))
    
    print("✅ Model files saved successfully!")
    print("📁 Files created:")
    print("   - df.pkl (preprocessed data)")
    print("   - pipe.pkl (RandomForestRegressor pipeline)")
    
    # Verify the saved model
    print("\n🔍 Verifying saved model...")
    loaded_pipe = pickle.load(open('pipe.pkl', 'rb'))
    print(f"   Loaded model type: {type(loaded_pipe.named_steps['step2'])}")
    
    # Test prediction
    test_pred = loaded_pipe.predict(X_test_processed[:1])
    print(f"   Test prediction successful: ₹{np.exp(test_pred[0]):,.0f}")
    
    return pipe, df

if __name__ == "__main__":
    print("🚀 Final Fix for Laptop Price Predictor")
    print("Running your exact training code to generate correct model files")
    print("=" * 70)
    
    try:
        pipeline, df = main()
        print("\n🎉 SUCCESS! Model files regenerated with RandomForestRegressor")
        print("\n📋 Next steps:")
        print("1. Run: streamlit run laptop_fixed.py")
        print("2. The ExtraTreesRegressor error should be completely resolved!")
        
    except FileNotFoundError:
        print("❌ Error: laptop_data.csv not found!")
        print("Please ensure the dataset file is in the current directory.")
        
    except Exception as e:
        print(f"❌ Error during training: {str(e)}")
        import traceback
        traceback.print_exc()
