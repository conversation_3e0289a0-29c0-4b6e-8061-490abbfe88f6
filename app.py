import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pickle

# Load model
pipe = pickle.load(open('pipe.pkl', 'rb'))

# Load data
match_data = pd.read_csv('matches.csv')
delivery_data = pd.read_csv('deliveries.csv')

# Merge data and preprocess (reuse your previous logic here or load a cleaned CSV)
# For demo, let’s assume you have `delivery_df` already cleaned

# Title
st.title('🏏 IPL Win Probability Predictor')

# Match selection
match_ids = delivery_data['match_id'].unique()
match_id = st.selectbox("Select a Match ID", match_ids)

# Predict and show progression
def match_progression(x_df, match_id, pipe):
    match = x_df[x_df['match_id'] == match_id]

    temp_df = match[['batting_team', 'bowling_team', 'city', 'runs_left', 'balls_left',
                     'wickets', 'total_runs_x', 'crr', 'rrr']]
    temp_df = temp_df[temp_df['balls_left'] != 0]

    if temp_df.empty:
        return pd.DataFrame(), None

    result = pipe.predict_proba(temp_df)
    temp_df['lose'] = np.round(result.T[0] * 100, 1)
    temp_df['win'] = np.round(result.T[1] * 100, 1)

    temp_df.reset_index(drop=True, inplace=True)
    temp_df['end_of_over'] = temp_df.index + 1

    target = temp_df['total_runs_x'].values[0]
    runs = list(temp_df['runs_left'].values)
    runs.insert(0, target)
    new_runs = runs[1:]
    reversed_runs = runs[::-1][:-1]

    temp_df['runs_after_over'] = np.array(reversed_runs) - np.array(new_runs)

    wickets = list(temp_df['wickets'].values)
    new_wickets = wickets[:]
    new_wickets.insert(0, 10)
    w = np.array(wickets)
    nw = np.array(new_wickets[1:])
    temp_df['wickets_in_over'] = new_wickets[:-1] - w

    return temp_df, target

# Generate chart
temp_df, target = match_progression(delivery_data, match_id, pipe)

if temp_df.empty:
    st.warning("No data available for the selected match.")
else:
    st.subheader(f"📊 Match Progression (Target: {target})")

    fig, ax = plt.subplots(figsize=(18, 8))
    ax.bar(temp_df['end_of_over'], temp_df['runs_after_over'], color='skyblue', label='Runs in Over')
    ax.plot(temp_df['end_of_over'], temp_df['win'], color='#a853a8', linewidth=3, label='Win %')
    ax.plot(temp_df['end_of_over'], temp_df['lose'], color='red', linewidth=3, label='Lose %')
    ax.plot(temp_df['end_of_over'], temp_df['wickets_in_over'], color='gold', linewidth=2, label='Wickets in Over')

    ax.set_title('Match Progression — Target: ' + str(target), fontsize=16)
    ax.set_xlabel('End of Over')
    ax.set_ylabel('Runs / Probability / Wickets')
    ax.legend()
    ax.grid(True)

    st.pyplot(fig)
