# -*- coding: utf-8 -*-
"""Language translation.ipynb

Automatically generated by <PERSON>b.

Original file is located at
    https://colab.research.google.com/drive/1W-LC1wVDVPHtvdEuRhyDBp2r8Qo9Z5N5
"""

!pip install deep_translator

from deep_translator import GoogleTranslator

text = "my name is <PERSON><PERSON><PERSON><PERSON>"

print(GoogleTranslator(source='en', target='ur').translate(text))

def trans(text, target_lang):
  trans_text =  GoogleTranslator(target = target_lang).translate(text)
  return trans_text

while True:
    print("\n Language Translation tool...")
    input_text = input("Enter your text....")
    target_lang = input("To translate lang....")

    if input_text == "exit":
        break

    trans_text = trans(input_text, target_lang)
    print("Translation :", trans_text)

