# Genetic Algorithm Implementation from Scratch
# Assignment #1 - Artificial Intelligence

import numpy as np
from numpy.random import randint, rand
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

print("=" * 60)
print("GENETIC ALGORITHM IMPLEMENTATION")
print("=" * 60)

# =============================================================================
# PART 1: GENETIC ALGORITHM FOR ONEMAX PROBLEM
# =============================================================================

print("\nPART 1: OneMax Problem Implementation")
print("-" * 40)

def onemax(x):
    """
    OneMax objective function - maximizes the number of 1s in a binary string
    Returns negative sum to convert maximization to minimization problem
    """
    return -sum(x)

def selection(pop, scores, k=3):
    """
    Tournament selection - selects k individuals randomly and returns the best one
    """
    # First random selection
    selection_ix = randint(len(pop))
    for ix in randint(0, len(pop), k-1):
        # Check if better (lower score for minimization)
        if scores[ix] < scores[selection_ix]:
            selection_ix = ix
    return pop[selection_ix]

def crossover(p1, p2, r_cross):
    """
    Single-point crossover between two parents
    """
    # Children are copies of parents by default
    c1, c2 = p1.copy(), p2.copy()
    # Check for recombination
    if rand() < r_cross:
        # Select crossover point that is not on the end of the string
        pt = randint(1, len(p1)-2)
        # Perform crossover
        c1 = p1[:pt] + p2[pt:]
        c2 = p2[:pt] + p1[pt:]
    return [c1, c2]

def mutation(bitstring, r_mut):
    """
    Bit-flip mutation operator
    """
    for i in range(len(bitstring)):
        # Check for a mutation
        if rand() < r_mut:
            # Flip the bit
            bitstring[i] = 1 - bitstring[i]

def genetic_algorithm_onemax(objective, n_bits, n_iter, n_pop, r_cross, r_mut):
    """
    Main genetic algorithm function for OneMax problem
    """
    # Initial population of random bitstrings
    pop = [randint(0, 2, n_bits).tolist() for _ in range(n_pop)]
    
    # Keep track of best solution and fitness history
    best, best_eval = 0, objective(pop[0])
    fitness_history = []
    
    print(f"Initial population size: {len(pop)}")
    print(f"Chromosome length: {n_bits} bits")
    print(f"Target fitness: {-n_bits} (all 1s)")
    print("\nEvolution Progress:")
    
    # Enumerate generations
    for gen in range(n_iter):
        # Evaluate all candidates in the population
        scores = [objective(c) for c in pop]
        
        # Track average fitness
        avg_fitness = sum(scores) / len(scores)
        fitness_history.append(avg_fitness)
        
        # Check for new best solution
        for i in range(n_pop):
            if scores[i] < best_eval:
                best, best_eval = pop[i], scores[i]
                print(f">Generation {gen}, new best: f({pop[i]}) = {scores[i]:.3f}")
        
        # Select parents
        selected = [selection(pop, scores) for _ in range(n_pop)]
        
        # Create the next generation
        children = list()
        for i in range(0, n_pop, 2):
            # Get selected parents in pairs
            p1, p2 = selected[i], selected[i+1]
            # Crossover and mutation
            for c in crossover(p1, p2, r_cross):
                # Mutation
                mutation(c, r_mut)
                # Store for next generation
                children.append(c)
        
        # Replace population
        pop = children
        
        # Early termination if optimal solution found
        if best_eval == -n_bits:
            print(f"Optimal solution found at generation {gen}!")
            break
    
    return [best, best_eval], fitness_history

# OneMax Problem Configuration
print("\nOneMax Configuration:")
n_iter = 100
n_bits = 20
n_pop = 100
r_cross = 0.9
r_mut = 1.0 / float(n_bits)

print(f"Iterations: {n_iter}")
print(f"Bits per chromosome: {n_bits}")
print(f"Population size: {n_pop}")
print(f"Crossover rate: {r_cross}")
print(f"Mutation rate: {r_mut:.4f}")

# Run OneMax genetic algorithm
result_onemax, fitness_history_onemax = genetic_algorithm_onemax(
    onemax, n_bits, n_iter, n_pop, r_cross, r_mut
)

print("\nOneMax Results:")
print(f"Best solution: {result_onemax[0]}")
print(f"Best fitness: {result_onemax[1]}")
print(f"Number of 1s: {sum(result_onemax[0])}")

# =============================================================================
# PART 2: GENETIC ALGORITHM FOR CONTINUOUS FUNCTION OPTIMIZATION
# =============================================================================

print("\n\nPART 2: Continuous Function Optimization (x² + y²)")
print("-" * 50)

def objective_continuous(x):
    """
    Continuous objective function: f(x,y) = x² + y²
    Minimum at (0,0) with f(0,0) = 0
    """
    return x[0]**2.0 + x[1]**2.0

def decode(bounds, n_bits, bitstring):
    """
    Decode bitstring to real numbers within specified bounds
    """
    decoded = list()
    largest = 2**n_bits
    for i in range(len(bounds)):
        # Extract the substring for this variable
        start, end = i * n_bits, (i * n_bits) + n_bits
        substring = bitstring[start:end]
        # Convert bitstring to a string of chars
        chars = ''.join([str(s) for s in substring])
        # Convert string to integer
        integer = int(chars, 2)
        # Scale integer to desired range
        value = bounds[i][0] + (integer/largest) * (bounds[i][1] - bounds[i][0])
        # Store
        decoded.append(value)
    return decoded

def genetic_algorithm_continuous(objective, bounds, n_bits, n_iter, n_pop, r_cross, r_mut):
    """
    Main genetic algorithm function for continuous optimization
    """
    # Initial population of random bitstrings
    pop = [randint(0, 2, n_bits*len(bounds)).tolist() for _ in range(n_pop)]
    
    # Keep track of best solution
    best, best_eval = 0, objective(decode(bounds, n_bits, pop[0]))
    fitness_history = []
    best_solutions = []
    
    print(f"Search space: {bounds}")
    print(f"Chromosome length: {n_bits * len(bounds)} bits ({n_bits} per variable)")
    print(f"Target: f(0,0) = 0")
    print("\nEvolution Progress:")
    
    # Enumerate generations
    for gen in range(n_iter):
        # Decode population
        decoded = [decode(bounds, n_bits, p) for p in pop]
        
        # Evaluate all candidates in the population
        scores = [objective(d) for d in decoded]
        
        # Track fitness history
        avg_fitness = sum(scores) / len(scores)
        fitness_history.append(avg_fitness)
        
        # Check for new best solution
        for i in range(n_pop):
            if scores[i] < best_eval:
                best, best_eval = pop[i], scores[i]
                decoded_best = decoded[i]
                best_solutions.append((gen, decoded_best, scores[i]))
                print(f">Generation {gen}, new best: f({decoded_best[0]:.6f}, {decoded_best[1]:.6f}) = {scores[i]:.6f}")
        
        # Select parents
        selected = [selection(pop, scores) for _ in range(n_pop)]
        
        # Create the next generation
        children = list()
        for i in range(0, n_pop, 2):
            # Get selected parents in pairs
            p1, p2 = selected[i], selected[i+1]
            # Crossover and mutation
            for c in crossover(p1, p2, r_cross):
                # Mutation
                mutation(c, r_mut)
                # Store for next generation
                children.append(c)
        
        # Replace population
        pop = children
    
    return [best, best_eval], fitness_history, best_solutions

# Continuous Function Configuration
print("\nContinuous Function Configuration:")
bounds = [[-5.0, 5.0], [-5.0, 5.0]]
n_iter = 100
n_bits = 16
n_pop = 100
r_cross = 0.9
r_mut = 1.0 / (float(n_bits) * len(bounds))

print(f"Variable bounds: {bounds}")
print(f"Iterations: {n_iter}")
print(f"Bits per variable: {n_bits}")
print(f"Population size: {n_pop}")
print(f"Crossover rate: {r_cross}")
print(f"Mutation rate: {r_mut:.6f}")

# Run continuous function genetic algorithm
result_continuous, fitness_history_continuous, best_solutions = genetic_algorithm_continuous(
    objective_continuous, bounds, n_bits, n_iter, n_pop, r_cross, r_mut
)

# Decode final result
final_decoded = decode(bounds, n_bits, result_continuous[0])

print("\nContinuous Function Results:")
print(f"Best solution (decoded): [{final_decoded[0]:.6f}, {final_decoded[1]:.6f}]")
print(f"Best fitness: {result_continuous[1]:.6f}")
print(f"Distance from optimal (0,0): {np.sqrt(final_decoded[0]**2 + final_decoded[1]**2):.6f}")

# =============================================================================
# VISUALIZATION AND ANALYSIS
# =============================================================================

print("\n\nGENERATING VISUALIZATIONS...")
print("-" * 30)

# Create comprehensive plots
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

# Plot 1: OneMax Fitness Evolution
ax1.plot(fitness_history_onemax, 'b-', linewidth=2, label='Average Fitness')
ax1.axhline(y=-n_bits, color='r', linestyle='--', linewidth=2, label='Optimal Fitness')
ax1.set_xlabel('Generation')
ax1.set_ylabel('Fitness')
ax1.set_title('OneMax Problem: Fitness Evolution')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Plot 2: Continuous Function Fitness Evolution
ax2.plot(fitness_history_continuous, 'g-', linewidth=2, label='Average Fitness')
ax2.axhline(y=0, color='r', linestyle='--', linewidth=2, label='Optimal Fitness')
ax2.set_xlabel('Generation')
ax2.set_ylabel('Fitness')
ax2.set_title('Continuous Function: Fitness Evolution')
ax2.set_yscale('log')
ax2.legend()
ax2.grid(True, alpha=0.3)

# Plot 3: OneMax Convergence Analysis
generations = list(range(len(fitness_history_onemax)))
ones_count = [-(fit) for fit in fitness_history_onemax]
ax3.plot(generations, ones_count, 'purple', linewidth=2, marker='o', markersize=3)
ax3.axhline(y=n_bits, color='r', linestyle='--', linewidth=2, label='Target (All 1s)')
ax3.set_xlabel('Generation')
ax3.set_ylabel('Average Number of 1s')
ax3.set_title('OneMax: Average Number of 1s per Generation')
ax3.legend()
ax3.grid(True, alpha=0.3)

# Plot 4: Continuous Function Search Space Visualization
if best_solutions:
    x_coords = [sol[1][0] for sol in best_solutions]
    y_coords = [sol[1][1] for sol in best_solutions]
    generations_best = [sol[0] for sol in best_solutions]
    
    # Create contour plot of the objective function
    x_range = np.linspace(-5, 5, 100)
    y_range = np.linspace(-5, 5, 100)
    X, Y = np.meshgrid(x_range, y_range)
    Z = X**2 + Y**2
    
    contour = ax4.contour(X, Y, Z, levels=20, colors='lightblue', alpha=0.6)
    ax4.plot(x_coords, y_coords, 'ro-', linewidth=2, markersize=6, label='Best Solutions Path')
    ax4.plot(0, 0, 'g*', markersize=15, label='Global Optimum (0,0)')
    ax4.plot(final_decoded[0], final_decoded[1], 'b^', markersize=10, label='Final Solution')
    
    ax4.set_xlabel('x')
    ax4.set_ylabel('y')
    ax4.set_title('Continuous Function: Search Path Visualization')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# =============================================================================
# PERFORMANCE ANALYSIS AND SUMMARY
# =============================================================================

print("\n\nPERFORMANCE ANALYSIS")
print("=" * 50)

print("\n1. OneMax Problem Analysis:")
print(f"   - Target: All 1s (fitness = {-n_bits})")
print(f"   - Achieved: {sum(result_onemax[0])} ones (fitness = {result_onemax[1]})")
print(f"   - Success Rate: {(sum(result_onemax[0])/n_bits)*100:.1f}%")
print(f"   - Convergence: {'Optimal' if result_onemax[1] == -n_bits else 'Near-optimal'}")

print("\n2. Continuous Function Analysis:")
print(f"   - Target: f(0,0) = 0")
print(f"   - Achieved: f({final_decoded[0]:.6f}, {final_decoded[1]:.6f}) = {result_continuous[1]:.6f}")
print(f"   - Distance from optimum: {np.sqrt(final_decoded[0]**2 + final_decoded[1]**2):.6f}")
print(f"   - Precision: {6 - int(np.log10(abs(result_continuous[1]) + 1e-10))} decimal places")

print("\n3. Algorithm Parameters Effectiveness:")
print(f"   - Population size ({n_pop}): Adequate for exploration")
print(f"   - Crossover rate ({r_cross}): High exploitation")
print(f"   - Mutation rates: Balanced exploration/exploitation")
print(f"   - Selection pressure: Tournament selection (k=3) provides good balance")

print("\n4. Key Observations:")
print("   - OneMax converged quickly due to linear objective function")
print("   - Continuous function required more generations for fine-tuning")
print("   - Both problems achieved near-optimal or optimal solutions")
print("   - Genetic operators worked synergistically for effective search")

print("\n" + "="*60)
print("GENETIC ALGORITHM IMPLEMENTATION COMPLETED SUCCESSFULLY")
print("="*60)