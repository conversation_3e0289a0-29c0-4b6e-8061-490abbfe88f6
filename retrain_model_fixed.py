#!/usr/bin/env python3
"""
Laptop Price Predictor Model Retraining Script
This script retrains the model exactly as in the original laptop_price_predictor1.py
"""

import pandas as pd
import numpy as np
import pickle
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.metrics import r2_score, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

def preprocess_data():
    """Preprocess data exactly as in the original training script"""
    print("📊 Loading and preprocessing data...")
    
    # Load the dataset
    df = pd.read_csv('laptop_data.csv')
    
    # Drop unnecessary columns
    if 'Unnamed: 0' in df.columns:
        df.drop(columns=['Unnamed: 0'], inplace=True)
    
    # Clean RAM and Weight columns
    df['Ram'] = df['Ram'].str.replace('GB', '').astype('int32')
    df['Weight'] = df['Weight'].str.replace('kg', '').astype('float32')
    
    # Extract touchscreen and IPS information
    df['Touchscreen'] = df['ScreenResolution'].apply(lambda x: 1 if 'Touchscreen' in x else 0)
    df['Ips'] = df['ScreenResolution'].apply(lambda x: 1 if 'IPS' in x else 0)
    
    # Extract screen resolution and calculate PPI
    new = df['ScreenResolution'].str.split('x', n=1, expand=True)
    df['X_res'] = new[0]
    df['Y_res'] = new[1]
    df['X_res'] = df['X_res'].str.replace(',', '').str.findall(r'(\d+\.?\d+)').apply(lambda x: x[0])
    df['X_res'] = df['X_res'].astype('int')
    df['Y_res'] = df['Y_res'].astype('int')
    df['ppi'] = (((df['X_res']**2) + (df['Y_res']**2))**0.5 / df['Inches']).astype('float')
    
    # Drop screen resolution related columns
    df.drop(columns=['ScreenResolution', 'Inches', 'X_res', 'Y_res'], inplace=True)
    
    # Process CPU
    df['Cpu Name'] = df['Cpu'].apply(lambda x: " ".join(x.split()[0:3]))
    
    def fetch_processor(text):
        if text == 'Intel Core i7' or text == 'Intel Core i5' or text == 'Intel Core i3':
            return text
        else:
            if text.split()[0] == 'Intel':
                return 'Other Intel Processor'
            else:
                return 'AMD Processor'
    
    df['Cpu brand'] = df['Cpu Name'].apply(fetch_processor)
    df.drop(columns=['Cpu', 'Cpu Name'], inplace=True)
    
    # Process Memory (Storage) - exactly as in original
    df['Memory'] = df['Memory'].astype(str).replace('\.0', '', regex=True)
    df["Memory"] = df["Memory"].str.replace('GB', '')
    df["Memory"] = df["Memory"].str.replace('TB', '000')
    new = df["Memory"].str.split("+", n=1, expand=True)
    
    df["first"] = new[0]
    df["first"] = df["first"].str.strip()
    df["second"] = new[1]
    
    df["Layer1HDD"] = df["first"].apply(lambda x: 1 if "HDD" in x else 0)
    df["Layer1SSD"] = df["first"].apply(lambda x: 1 if "SSD" in x else 0)
    df["Layer1Hybrid"] = df["first"].apply(lambda x: 1 if "Hybrid" in x else 0)
    df["Layer1Flash_Storage"] = df["first"].apply(lambda x: 1 if "Flash Storage" in x else 0)
    
    df['first'] = df['first'].str.replace(r'\D', '', regex=True)
    df["second"] = df["second"].fillna("0").str.strip()
    
    df["Layer2HDD"] = df["second"].apply(lambda x: 1 if "HDD" in x else 0)
    df["Layer2SSD"] = df["second"].apply(lambda x: 1 if "SSD" in x else 0)
    df["Layer2Hybrid"] = df["second"].apply(lambda x: 1 if "Hybrid" in x else 0)
    df["Layer2Flash_Storage"] = df["second"].apply(lambda x: 1 if "Flash Storage" in x else 0)
    
    df['second'] = df['second'].str.replace(r'\D', '', regex=True)
    df["first"] = df["first"].astype(int)
    df["second"] = df["second"].astype(int)
    
    df["HDD"] = (df["first"] * df["Layer1HDD"] + df["second"] * df["Layer2HDD"])
    df["SSD"] = (df["first"] * df["Layer1SSD"] + df["second"] * df["Layer2SSD"])
    df["Hybrid"] = (df["first"] * df["Layer1Hybrid"] + df["second"] * df["Layer2Hybrid"])
    df["Flash_Storage"] = (df["first"] * df["Layer1Flash_Storage"] + df["second"] * df["Layer2Flash_Storage"])
    
    df.drop(columns=['first', 'second', 'Layer1HDD', 'Layer1SSD', 'Layer1Hybrid',
                     'Layer1Flash_Storage', 'Layer2HDD', 'Layer2SSD', 'Layer2Hybrid',
                     'Layer2Flash_Storage'], inplace=True)
    
    df.drop(columns=['Memory'], inplace=True)
    df.drop(columns=['Hybrid', 'Flash_Storage'], inplace=True)
    
    # Process GPU
    df['Gpu brand'] = df['Gpu'].apply(lambda x: x.split()[0])
    df = df[df['Gpu brand'] != 'ARM']
    df.drop(columns=['Gpu'], inplace=True)
    
    # Process Operating System
    def cat_os(inp):
        if inp == 'Windows 10' or inp == 'Windows 7' or inp == 'Windows 10 S':
            return 'Windows'
        elif inp == 'macOS' or inp == 'Mac OS X':
            return 'Mac'
        else:
            return 'Others/No OS/Linux'
    
    df['os'] = df['OpSys'].apply(cat_os)
    df.drop(columns=['OpSys'], inplace=True)
    
    print(f"✅ Data preprocessing complete. Shape: {df.shape}")
    print(f"📋 Columns: {df.columns.tolist()}")
    return df

def train_model():
    """Train the model exactly as in the original script"""
    print("🚀 Starting model training...")
    
    # Preprocess data
    df = preprocess_data()
    
    # Prepare features and target
    X = df.drop(columns=['Price'])
    y = np.log(df['Price'])
    
    print(f"📊 Features shape: {X.shape}")
    print(f"📊 Feature columns: {X.columns.tolist()}")
    
    # Split the data (same as original)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.15, random_state=2)
    
    # Create the exact pipeline from the original script
    step1 = ColumnTransformer(transformers=[
        ('col_tnf', OneHotEncoder(sparse_output=False, drop='first'), [0, 1, 7, 10, 11])
    ], remainder='passthrough')
    
    step2 = RandomForestRegressor(
        n_estimators=100,
        random_state=3,
        max_samples=0.5,
        max_features=0.75,
        max_depth=15
    )
    
    pipe = Pipeline([
        ('step1', step1),
        ('step2', step2)
    ])
    
    # Train the model
    print("🎯 Training model...")
    pipe.fit(X_train, y_train)
    
    # Make predictions
    y_pred = pipe.predict(X_test)
    
    # Calculate metrics
    r2 = r2_score(y_test, y_pred)
    mae = mean_absolute_error(y_test, y_pred)
    
    print(f"📈 Model Performance:")
    print(f"   R² Score: {r2:.4f}")
    print(f"   Mean Absolute Error: {mae:.4f}")
    
    # Save the model and data
    print("💾 Saving model and data...")
    
    # Save the dataframe (for UI dropdowns)
    with open('df.pkl', 'wb') as f:
        pickle.dump(df, f)
    
    # Save the pipeline
    with open('pipe.pkl', 'wb') as f:
        pickle.dump(pipe, f)
    
    print("✅ Model training complete!")
    print("📁 Files saved:")
    print("   - df.pkl (preprocessed data)")
    print("   - pipe.pkl (trained model pipeline)")
    
    return pipe, df

if __name__ == "__main__":
    print("🔄 Retraining Laptop Price Predictor Model")
    print("=" * 50)
    
    try:
        pipeline, df = train_model()
        print("\n🎉 Success! You can now run the Streamlit app:")
        print("   streamlit run laptop_fixed.py")
        
    except FileNotFoundError:
        print("❌ Error: laptop_data.csv not found!")
        print("Please ensure the dataset file is in the current directory.")
        
    except Exception as e:
        print(f"❌ Error during training: {str(e)}")
        print("Please check your data and try again.")
