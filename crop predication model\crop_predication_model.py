# -*- coding: utf-8 -*-
"""Crop predication model.ipynb

Automatically generated by <PERSON>b.

Original file is located at
    https://colab.research.google.com/drive/1x27YgNw_RjmDuCXG5pUFlL31HyliyDTv
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
import joblib

df = pd.read_csv("Crop_recommendation.csv")  

print(df.info())
print(df.head())


print("Missing values:\n", df.isnull().sum())


le = LabelEncoder()
df['label_encoded'] = le.fit_transform(df['label'])


X = df.drop(['label', 'label_encoded'], axis=1)
y = df['label_encoded']


print("Label mapping:", dict(zip(le.classes_, le.transform(le.classes_))))


X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42
)

print("Training Features:", X_train.shape)
print("Testing Features:", X_test.shape)


model = RandomForestClassifier(n_estimators=100, random_state=42)
model.fit(X_train, y_train)

print("Model trained successfully ")


y_pred = model.predict(X_test)

# Accuracy
acc = accuracy_score(y_test, y_pred)
print("Accuracy:", acc)


print("Classification Report:\n", classification_report(y_test, y_pred, target_names=le.classes_))


joblib.dump(model, 'crop_model.pkl')

joblib.dump(le, 'label_encoder.pkl')

print("Model and encoder saved successfully ")

