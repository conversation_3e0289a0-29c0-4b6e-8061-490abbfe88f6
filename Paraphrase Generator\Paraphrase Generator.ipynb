{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4", "mount_file_id": "1uKKyWLOk3XZHFKvSIg9mOGrhOkM_nao3", "authorship_tag": "ABX9TyO0lUo89IqPQZynWCPqXzcp"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8kELZBInbtdl", "executionInfo": {"status": "ok", "timestamp": 1750961602501, "user_tz": -300, "elapsed": 14456, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "01972503468438909337"}}, "outputId": "2b72cdf7-1be5-4b4a-de71-649605fc6921"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting sentence-splitter\n", "  Downloading sentence_splitter-1.4-py2.py3-none-any.whl.metadata (2.8 kB)\n", "Requirement already satisfied: regex>=2017.12.12 in /usr/local/lib/python3.11/dist-packages (from sentence-splitter) (2024.11.6)\n", "Downloading sentence_splitter-1.4-py2.py3-none-any.whl (44 kB)\n", "\u001b[?25l   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/45.0 kB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m45.0/45.0 kB\u001b[0m \u001b[31m3.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: sentence-splitter\n", "Successfully installed sentence-splitter-1.4\n", "Requirement already satisfied: transformers in /usr/local/lib/python3.11/dist-packages (4.52.4)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from transformers) (3.18.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.33.0)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2.0.2)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2024.11.6)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.21.1)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.5.3)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.11/dist-packages (from transformers) (4.67.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (2025.3.2)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (4.14.0)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (1.1.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2025.6.15)\n", "Requirement already satisfied: SentencePiece in /usr/local/lib/python3.11/dist-packages (0.2.0)\n"]}], "source": ["! pip install sentence-splitter\n", "! pip install transformers\n", "! pip install SentencePiece"]}, {"cell_type": "code", "source": ["import torch\n", "from transformers import PegasusForConditionalGeneration, PegasusTokenizer"], "metadata": {"id": "dJ-HWrZGcEjq"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["model = PegasusForConditionalGeneration.from_pretrained('tuner007/pegasus_paraphrase')\n", "tokenizer = PegasusTokenizer.from_pretrained('tuner007/pegasus_paraphrase')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tomG9tMbcoPE", "executionInfo": {"status": "ok", "timestamp": 1750964563028, "user_tz": -300, "elapsed": 3540, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "01972503468438909337"}}, "outputId": "3e432d0a-96de-4e4e-fff2-dff23c39a305"}, "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["Some weights of PegasusForConditionalGeneration were not initialized from the model checkpoint at tuner007/pegasus_paraphrase and are newly initialized: ['model.decoder.embed_positions.weight', 'model.encoder.embed_positions.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}]}, {"cell_type": "code", "source": ["text = \"The ultimate test of your knowledge is your capacity to convey it to another.\"\n", "\n", "batch = tokenizer([text], padding=True, truncation=True, max_length=60, return_tensors='pt')\n", "\n", "output = model.generate(input_ids=batch['input_ids'],\n", "                        attention_mask=batch['attention_mask'],\n", "                        max_length=60,\n", "                        num_beams=5,\n", "                        num_return_sequences=5,\n", "                        temperature=1.5)\n"], "metadata": {"id": "KGEI3YfKkwTB", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1750964577161, "user_tz": -300, "elapsed": 4373, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "01972503468438909337"}}, "outputId": "bd087622-8043-43c0-f1eb-112dcde931f0"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["The following generation flags are not valid and may be ignored: ['temperature']. Set `TRANSFORMERS_VERBOSITY=info` for more details.\n"]}]}, {"cell_type": "code", "source": ["result = tokenizer.batch_decode(output, skip_special_tokens=True)\n", "result"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "likpt85-nIVg", "executionInfo": {"status": "ok", "timestamp": 1750964823605, "user_tz": -300, "elapsed": 31, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "01972503468438909337"}}, "outputId": "743a6b01-471c-4b01-bef2-2f44ec04fbe4"}, "execution_count": 14, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["['The test of your knowledge is your ability to convey it.',\n", " 'The ability to convey your knowledge is the ultimate test of your knowledge.',\n", " 'The test of your knowledge is your ability to communicate it.',\n", " 'Your capacity to convey your knowledge is the ultimate test of your knowledge.',\n", " 'The test of your knowledge is how well you can convey it.']"]}, "metadata": {}, "execution_count": 14}]}, {"cell_type": "code", "source": ["# save model and tokenizer\n", "model.save_pretrained('/content/drive/MyDrive/paraphrase_model')\n", "tokenizer.save_pretrained('/content/drive/MyDrive/paraphrase_tokenizer')\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "LRlwBBV3oq4H", "executionInfo": {"status": "ok", "timestamp": 1750967158622, "user_tz": -300, "elapsed": 24418, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "01972503468438909337"}}, "outputId": "e4252738-7484-48c7-fe1f-63f1ffacc466"}, "execution_count": 24, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["('/content/drive/MyDrive/paraphrase_tokenizer/tokenizer_config.json',\n", " '/content/drive/MyDrive/paraphrase_tokenizer/special_tokens_map.json',\n", " '/content/drive/MyDrive/paraphrase_tokenizer/spiece.model',\n", " '/content/drive/MyDrive/paraphrase_tokenizer/added_tokens.json')"]}, "metadata": {}, "execution_count": 24}]}, {"cell_type": "code", "source": ["# predictive system\n", "def get_response(input_text, num_return_sequences=5, num_beams=5):\n", "  batch = tokenizer([input_text], padding=True, truncation=True, max_length=60, return_tensors='pt')\n", "\n", "  translated = model.generate(input_ids=batch['input_ids'],\n", "                        attention_mask=batch['attention_mask'],\n", "                        max_length=60,\n", "                        num_beams=num_beams,\n", "                        num_return_sequences=num_return_sequences,\n", "                        temperature=1.5)\n", "  tgt_text = tokenizer.batch_decode(translated, skip_special_tokens=True)\n", "  return tgt_text\n", "\n", "\n"], "metadata": {"id": "mnZSmo3to_iH", "executionInfo": {"status": "ok", "timestamp": 1750967163513, "user_tz": -300, "elapsed": 31, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "01972503468438909337"}}}, "execution_count": 25, "outputs": []}, {"cell_type": "code", "source": ["num_beams = 10\n", "num_return_sequences = 10\n", "context = \"Machine learning is a growing field in AI\"\n", "\n", "get_response(context, num_return_sequences, num_beams)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aNWeRvV3qWf0", "executionInfo": {"status": "ok", "timestamp": 1750967172428, "user_tz": -300, "elapsed": 5851, "user": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "userId": "01972503468438909337"}}, "outputId": "6d9b4d31-d855-44f5-cc4d-35a996dd5862"}, "execution_count": 26, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["The following generation flags are not valid and may be ignored: ['temperature']. Set `TRANSFORMERS_VERBOSITY=info` for more details.\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["['There is a growing field of machine learning.',\n", " 'The field of machine learning is growing.',\n", " 'Machine learning is growing.',\n", " 'Machine learning is growing in popularity.',\n", " 'Machine learning is a growing field.',\n", " 'Machine learning is becoming more and more popular.',\n", " 'Machine learning is an emerging field.',\n", " 'Machine learning is a field that is growing.',\n", " 'There is a growing field of machine learning',\n", " 'The field of machine learning is growing fast.']"]}, "metadata": {}, "execution_count": 26}]}, {"cell_type": "code", "source": [], "metadata": {"id": "sdd4HJ8kuWZl"}, "execution_count": null, "outputs": []}]}