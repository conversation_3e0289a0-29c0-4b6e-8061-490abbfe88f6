# <!DOCTYPE html>
# <html lang="en">
# <head>
#     <meta charset="UTF-8">
#     <meta name="viewport" content="width=device-width, initial-scale=1.0">
#     <title>Laptop Price Predictor</title>
#     <style>
#         * {
#             margin: 0;
#             padding: 0;
#             box-sizing: border-box;
#         }

#         body {
#             font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
#             background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
#             min-height: 100vh;
#             padding: 20px;
#         }

#         .container {
#             max-width: 1200px;
#             margin: 0 auto;
#             background: white;
#             border-radius: 20px;
#             box-shadow: 0 20px 40px rgba(0,0,0,0.1);
#             overflow: hidden;
#         }

#         .header {
#             background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
#             color: white;
#             padding: 30px;
#             text-align: center;
#         }

#         .header h1 {
#             font-size: 2.5em;
#             margin-bottom: 10px;
#             font-weight: 300;
#         }

#         .header p {
#             font-size: 1.1em;
#             opacity: 0.9;
#         }

#         .form-container {
#             display: grid;
#             grid-template-columns: 1fr 1fr;
#             gap: 40px;
#             padding: 40px;
#         }

#         .form-section {
#             background: #f8f9fa;
#             padding: 30px;
#             border-radius: 15px;
#             border: 1px solid #e9ecef;
#         }

#         .form-section h3 {
#             color: #2c3e50;
#             margin-bottom: 20px;
#             font-size: 1.3em;
#             display: flex;
#             align-items: center;
#             gap: 10px;
#         }

#         .form-group {
#             margin-bottom: 20px;
#         }

#         .form-group label {
#             display: block;
#             margin-bottom: 8px;
#             font-weight: 500;
#             color: #34495e;
#         }

#         .form-group select,
#         .form-group input {
#             width: 100%;
#             padding: 12px 15px;
#             border: 2px solid #e9ecef;
#             border-radius: 8px;
#             font-size: 16px;
#             transition: all 0.3s ease;
#             background: white;
#         }

#         .form-group select:focus,
#         .form-group input:focus {
#             outline: none;
#             border-color: #3498db;
#             box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
#         }

#         .checkbox-group {
#             display: flex;
#             align-items: center;
#             gap: 10px;
#             margin-bottom: 15px;
#         }

#         .checkbox-group input[type="checkbox"] {
#             width: auto;
#             margin: 0;
#             transform: scale(1.2);
#         }

#         .predict-section {
#             grid-column: 1 / -1;
#             text-align: center;
#             padding: 30px;
#             background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
#             border-radius: 15px;
#             margin-top: 20px;
#         }

#         .predict-btn {
#             background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
#             color: white;
#             border: none;
#             padding: 15px 40px;
#             font-size: 1.1em;
#             border-radius: 50px;
#             cursor: pointer;
#             transition: all 0.3s ease;
#             box-shadow: 0 5px 15px rgba(46, 204, 113, 0.3);
#         }

#         .predict-btn:hover {
#             transform: translateY(-2px);
#             box-shadow: 0 8px 25px rgba(46, 204, 113, 0.4);
#         }

#         .predict-btn:active {
#             transform: translateY(0);
#         }

#         .result {
#             margin-top: 30px;
#             padding: 20px;
#             background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
#             color: white;
#             border-radius: 15px;
#             font-size: 1.3em;
#             display: none;
#         }

#         .result.show {
#             display: block;
#             animation: slideIn 0.5s ease;
#         }

#         @keyframes slideIn {
#             from {
#                 opacity: 0;
#                 transform: translateY(20px);
#             }
#             to {
#                 opacity: 1;
#                 transform: translateY(0);
#             }
#         }

#         .icon {
#             font-size: 1.2em;
#         }

#         @media (max-width: 768px) {
#             .form-container {
#                 grid-template-columns: 1fr;
#                 gap: 20px;
#                 padding: 20px;
#             }
            
#             .header h1 {
#                 font-size: 2em;
#             }
            
#             .form-section {
#                 padding: 20px;
#             }
#         }
#     </style>
# </head>
# <body>
#     <div class="container">
#         <div class="header">
#             <h1>💻 Laptop Price Predictor</h1>
#             <p>Get accurate price predictions for laptops using advanced machine learning</p>
#         </div>

#         <div class="form-container">
#             <div class="form-section">
#                 <h3><span class="icon">🏢</span> Basic Information</h3>
                
#                 <div class="form-group">
#                     <label for="company">Company</label>
#                     <select id="company">
#                         <option value="">Select Company</option>
#                         <option value="Apple">Apple</option>
#                         <option value="HP">HP</option>
#                         <option value="Acer">Acer</option>
#                         <option value="Asus">Asus</option>
#                         <option value="Dell">Dell</option>
#                         <option value="Lenovo">Lenovo</option>
#                         <option value="MSI">MSI</option>
#                         <option value="Toshiba">Toshiba</option>
#                         <option value="Samsung">Samsung</option>
#                         <option value="Razer">Razer</option>
#                         <option value="Alienware">Alienware</option>
#                         <option value="Microsoft">Microsoft</option>
#                     </select>
#                 </div>

#                 <div class="form-group">
#                     <label for="typename">Type</label>
#                     <select id="typename">
#                         <option value="">Select Type</option>
#                         <option value="Ultrabook">Ultrabook</option>
#                         <option value="Notebook">Notebook</option>
#                         <option value="Gaming">Gaming</option>
#                         <option value="2 in 1 Convertible">2 in 1 Convertible</option>
#                         <option value="Workstation">Workstation</option>
#                         <option value="Netbook">Netbook</option>
#                     </select>
#                 </div>

#                 <div class="form-group">
#                     <label for="ram">RAM (GB)</label>
#                     <select id="ram">
#                         <option value="">Select RAM</option>
#                         <option value="4">4 GB</option>
#                         <option value="8">8 GB</option>
#                         <option value="12">12 GB</option>
#                         <option value="16">16 GB</option>
#                         <option value="24">24 GB</option>
#                         <option value="32">32 GB</option>
#                         <option value="64">64 GB</option>
#                     </select>
#                 </div>

#                 <div class="form-group">
#                     <label for="weight">Weight (kg)</label>
#                     <input type="number" id="weight" step="0.1" min="0.5" max="5" placeholder="e.g., 1.5">
#                 </div>
#             </div>

#             <div class="form-section">
#                 <h3><span class="icon">⚙️</span> Technical Specifications</h3>
                
#                 <div class="form-group">
#                     <label for="cpu">CPU Brand</label>
#                     <select id="cpu">
#                         <option value="">Select CPU</option>
#                         <option value="Intel Core i3">Intel Core i3</option>
#                         <option value="Intel Core i5">Intel Core i5</option>
#                         <option value="Intel Core i7">Intel Core i7</option>
#                         <option value="Other Intel Processor">Other Intel Processor</option>
#                         <option value="AMD Processor">AMD Processor</option>
#                     </select>
#                 </div>

#                 <div class="form-group">
#                     <label for="gpu">GPU Brand</label>
#                     <select id="gpu">
#                         <option value="">Select GPU</option>
#                         <option value="Intel">Intel</option>
#                         <option value="AMD">AMD</option>
#                         <option value="Nvidia">Nvidia</option>
#                     </select>
#                 </div>

#                 <div class="form-group">
#                     <label for="os">Operating System</label>
#                     <select id="os">
#                         <option value="">Select OS</option>
#                         <option value="Windows">Windows</option>
#                         <option value="Mac">Mac</option>
#                         <option value="Others/No OS/Linux">Others/No OS/Linux</option>
#                     </select>
#                 </div>

#                 <div class="form-group">
#                     <label for="ppi">PPI (Pixels Per Inch)</label>
#                     <input type="number" id="ppi" step="0.1" min="50" max="500" placeholder="e.g., 141.2">
#                 </div>
#             </div>

#             <div class="form-section">
#                 <h3><span class="icon">🖥️</span> Display Features</h3>
                
#                 <div class="checkbox-group">
#                     <input type="checkbox" id="touchscreen">
#                     <label for="touchscreen">Touchscreen</label>
#                 </div>

#                 <div class="checkbox-group">
#                     <input type="checkbox" id="ips">
#                     <label for="ips">IPS Display</label>
#                 </div>
#             </div>

#             <div class="form-section">
#                 <h3><span class="icon">💾</span> Storage Options</h3>
                
#                 <div class="form-group">
#                     <label for="hdd">HDD Storage (GB)</label>
#                     <input type="number" id="hdd" min="0" max="10000" step="128" placeholder="e.g., 1000 (0 for none)">
#                 </div>

#                 <div class="form-group">
#                     <label for="ssd">SSD Storage (GB)</label>
#                     <input type="number" id="ssd" min="0" max="5000" step="128" placeholder="e.g., 256 (0 for none)">
#                 </div>
#             </div>

#             <div class="predict-section">
#                 <button class="predict-btn" onclick="predictPrice()">
#                     🔮 Predict Price
#                 </button>
#                 <div id="result" class="result"></div>
#             </div>
#         </div>
#     </div>

#     <script>
#         function predictPrice() {
#             // Get all form values
#             const company = document.getElementById('company').value;
#             const typename = document.getElementById('typename').value;
#             const ram = document.getElementById('ram').value;
#             const weight = document.getElementById('weight').value;
#             const cpu = document.getElementById('cpu').value;
#             const gpu = document.getElementById('gpu').value;
#             const os = document.getElementById('os').value;
#             const ppi = document.getElementById('ppi').value;
#             const touchscreen = document.getElementById('touchscreen').checked;
#             const ips = document.getElementById('ips').checked;
#             const hdd = document.getElementById('hdd').value || 0;
#             const ssd = document.getElementById('ssd').value || 0;

#             // Validate required fields
#             if (!company || !typename || !ram || !weight || !cpu || !gpu || !os || !ppi) {
#                 alert('Please fill in all required fields!');
#                 return;
#             }

#             // Mock prediction logic (replace with actual API call)
#             const basePrice = getBasePrice(company, typename, cpu, gpu);
#             const ramMultiplier = getRamMultiplier(parseInt(ram));
#             const storageBonus = getStorageBonus(parseInt(hdd), parseInt(ssd));
#             const featureBonus = getFeatureBonus(touchscreen, ips);
#             const weightFactor = getWeightFactor(parseFloat(weight));
#             const ppiFactor = getPpiFactor(parseFloat(ppi));
            
#             const predictedPrice = Math.round(basePrice * ramMultiplier * weightFactor * ppiFactor + storageBonus + featureBonus);

#             // Display result
#             const resultDiv = document.getElementById('result');
#             resultDiv.innerHTML = `
#                 <h3>💰 Predicted Price</h3>
#                 <div style="font-size: 2em; margin: 15px 0; font-weight: bold;">
#                     $${predictedPrice.toLocaleString()}
#                 </div>
#                 <p>Based on the specifications you provided</p>
#             `;
#             resultDiv.classList.add('show');
#         }

#         // Mock pricing functions (replace with actual model predictions)
#         function getBasePrice(company, typename, cpu, gpu) {
#             let base = 500;
            
#             // Company multiplier
#             const companyMultipliers = {
#                 'Apple': 1.8, 'Alienware': 1.7, 'Razer': 1.6, 'MSI': 1.4,
#                 'HP': 1.2, 'Dell': 1.2, 'Asus': 1.1, 'Lenovo': 1.1,
#                 'Acer': 1.0, 'Toshiba': 1.0, 'Samsung': 1.3, 'Microsoft': 1.4
#             };
            
#             // Type multiplier
#             const typeMultipliers = {
#                 'Gaming': 1.5, 'Workstation': 1.4, '2 in 1 Convertible': 1.3,
#                 'Ultrabook': 1.2, 'Notebook': 1.0, 'Netbook': 0.7
#             };
            
#             // CPU multiplier
#             const cpuMultipliers = {
#                 'Intel Core i7': 1.5, 'Intel Core i5': 1.2, 'Intel Core i3': 1.0,
#                 'AMD Processor': 1.1, 'Other Intel Processor': 1.0
#             };
            
#             // GPU multiplier
#             const gpuMultipliers = {
#                 'Nvidia': 1.4, 'AMD': 1.2, 'Intel': 1.0
#             };
            
#             return base * (companyMultipliers[company] || 1) * (typeMultipliers[typename] || 1) * 
#                    (cpuMultipliers[cpu] || 1) * (gpuMultipliers[gpu] || 1);
#         }

#         function getRamMultiplier(ram) {
#             const ramMultipliers = {4: 1.0, 8: 1.3, 12: 1.5, 16: 1.8, 24: 2.2, 32: 2.8, 64: 4.0};
#             return ramMultipliers[ram] || 1.0;
#         }

#         function getStorageBonus(hdd, ssd) {
#             return (hdd * 0.05) + (ssd * 0.2);
#         }

#         function getFeatureBonus(touchscreen, ips) {
#             let bonus = 0;
#             if (touchscreen) bonus += 100;
#             if (ips) bonus += 50;
#             return bonus;
#         }

#         function getWeightFactor(weight) {
#             if (weight < 1.5) return 1.2;
#             if (weight < 2.5) return 1.0;
#             return 0.9;
#         }

#         function getPpiFactor(ppi) {
#             if (ppi > 200) return 1.2;
#             if (ppi > 150) return 1.1;
#             return 1.0;
#         }
#     </script>
# </body>
# </html>









# -*- coding: utf-8 -*-
import streamlit as st
import pickle
import numpy as np
import pandas as pd

# Load the model and dataframe
df = pickle.load(open('df.pkl', 'rb'))
pipe = pickle.load(open('pipe.pkl', 'rb'))

st.set_page_config(page_title="Laptop Price Predictor", page_icon="💻", layout="wide")

# Custom CSS for styling
st.markdown("""
    <style>
    .main {
        background-color: #f5f5f5;
    }
    .stSelectbox, .stNumberInput, .stSlider {
        background-color: white;
        border-radius: 5px;
        padding: 10px;
    }
    .css-1aumxhk {
        background-color: #0e1117;
        color: white;
    }
    h1 {
        color: #2a3f5f;
    }
    </style>
    """, unsafe_allow_html=True)

st.title("💻 Laptop Price Predictor")
st.markdown("Predict the price of your dream laptop based on its specifications")

# Layout with columns
col1, col2 = st.columns(2)

with col1:
    # Brand selection
    company = st.selectbox('Brand', df['Company'].unique())
    
    # Type of laptop
    type = st.selectbox('Type', df['TypeName'].unique())
    
    # RAM
    ram = st.selectbox('RAM (in GB)', [2, 4, 6, 8, 12, 16, 24, 32, 64])
    
    # Weight
    weight = st.number_input('Weight (kg)', min_value=0.5, max_value=5.0, value=2.0, step=0.1)
    
    # Touchscreen
    touchscreen = st.selectbox('Touchscreen', ['No', 'Yes'])

with col2:
    # IPS display
    ips = st.selectbox('IPS Display', ['No', 'Yes'])
    
    # Screen size (PPI will be calculated)
    screen_size = st.number_input('Screen Size (inches)', min_value=10.0, max_value=20.0, value=15.6, step=0.1)
    
    # Resolution
    resolution = st.selectbox('Screen Resolution', 
                             ['1920x1080', '1366x768', '1600x900', '3840x2160', '3200x1800', '2880x1800', '2560x1600', '2560x1440', '2304x1440'])
    
    # CPU brand
    cpu = st.selectbox('CPU Brand', df['Cpu brand'].unique())
    
    # HDD
    hdd = st.selectbox('HDD (in GB)', [0, 128, 256, 512, 1024, 2048])

# Additional specifications
with st.expander("Advanced Specifications"):
    col3, col4 = st.columns(2)
    
    with col3:
        # SSD
        ssd = st.selectbox('SSD (in GB)', [0, 8, 128, 256, 512, 1024])
        
    with col4:
        # GPU brand
        gpu = st.selectbox('GPU Brand', df['Gpu brand'].unique())
        
        # OS
        os = st.selectbox('Operating System', df['os'].unique())

# Prediction button
if st.button('Predict Price', type="primary"):
    try:
        # Calculate PPI
        X_res = int(resolution.split('x')[0])
        Y_res = int(resolution.split('x')[1])
        ppi = ((X_res**2) + (Y_res**2))**0.5 / screen_size
        
        # Prepare query
        query = pd.DataFrame({
            'Company': [company],
            'TypeName': [type],
            'Ram': [ram],
            'Weight': [weight],
            'Touchscreen': [1 if touchscreen == 'Yes' else 0],
            'Ips': [1 if ips == 'Yes' else 0],
            'ppi': [ppi],
            'Cpu brand': [cpu],
            'HDD': [hdd],
            'SSD': [ssd],
            'Gpu brand': [gpu],
            'os': [os]
        })
        
        # Make prediction
        prediction = np.exp(pipe.predict(query))
        
        # Display result
        st.success(f"Predicted Price: ₹{prediction[0]:,.2f}")
        
        # Show feature importance or other insights if available
        st.markdown("### Price Breakdown")
        st.write("""
        - Higher RAM and SSD typically increase price
        - Premium brands and gaming laptops command higher prices
        - Touchscreens and high-resolution displays add to the cost
        """)
        
    except Exception as e:
        st.error(f"An error occurred: {str(e)}")

# Add some information about the model
st.markdown("---")
st.markdown("""
### About This Predictor
This tool uses a machine learning model trained on thousands of laptop configurations to predict prices.
The model considers specifications like:
- Brand and type
- Processor and GPU
- Memory and storage
- Display features
- Weight and size
""")

# Add a footer
st.markdown("---")
st.markdown("© 2023 Laptop Price Predictor | Made with Streamlit")