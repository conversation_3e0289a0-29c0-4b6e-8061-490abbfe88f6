import streamlit as st
import numpy as np
from utils import load_models, get_recommendations
from styles import apply_custom_styles

# Set page configuration
st.set_page_config(
    page_title="Education Recommendation System",
    page_icon="🎓",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Apply custom styles
apply_custom_styles()

def main():
    # Title with custom styling
    st.markdown("""
    <div class="main-header">
        <h1>🎓 Education Recommendation System</h1>
        <p>Discover your ideal career path based on your academic performance and personal profile</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Load models
    scaler, model = load_models()
    
    if scaler is None or model is None:
        st.error("⚠️ Models not found! Please ensure the Models folder contains scaler.pkl and model.pkl files.")
        return
    
    # Create form layout
    with st.form("recommendation_form"):
        # Create two columns for the entire form
        col1, col2 = st.columns(2)
        
        with col1:
            gender = st.selectbox(
                "Gender:",
                options=["Female", "Male"],
                index=0
            )
            
            absence_days = st.number_input(
                "Absence Days:",
                min_value=0,
                max_value=365,
                value=2,
                step=1
            )
            
            weekly_study_hours = st.number_input(
                "Weekly Self-Study Hours:",
                min_value=0,
                max_value=50,
                value=4,
                step=1
            )
            
            history_score = st.number_input(
                "History Score:",
                min_value=0,
                max_value=100,
                value=73,
                step=1
            )
            
            chemistry_score = st.number_input(
                "Chemistry Score:",
                min_value=0,
                max_value=100,
                value=91,
                step=1
            )
            
            english_score = st.number_input(
                "English Score:",
                min_value=0,
                max_value=100,
                value=60,
                step=1
            )
        
        with col2:
            part_time_job = st.selectbox(
                "Part-time Job:",
                options=["No", "Yes"],
                index=0
            )
            
            extracurricular_activities = st.selectbox(
                "Extracurricular Activities:",
                options=["No", "Yes"],
                index=0
            )
            
            math_score = st.number_input(
                "Math Score:",
                min_value=0,
                max_value=100,
                value=85,
                step=1
            )
            
            physics_score = st.number_input(
                "Physics Score:",
                min_value=0,
                max_value=100,
                value=98,
                step=1
            )
            
            biology_score = st.number_input(
                "Biology Score:",
                min_value=0,
                max_value=100,
                value=79,
                step=1
            )
            
            geography_score = st.number_input(
                "Geography Score:",
                min_value=0,
                max_value=100,
                value=67,
                step=1
            )
        
        # Auto-calculate totals
        total_score = math_score + history_score + physics_score + chemistry_score + biology_score + english_score + geography_score
        average_score = total_score / 7
        
        # Display calculated values in two columns
        st.markdown('<div class="divider"></div>', unsafe_allow_html=True)
        
        col1, col2 = st.columns(2)
        with col1:
            st.markdown(f'<div class="summary-field"><strong>Total Score:</strong></div>', unsafe_allow_html=True)
            st.markdown(f'<div class="summary-value">{total_score:.2f}</div>', unsafe_allow_html=True)
        
        with col2:
            st.markdown(f'<div class="summary-field"><strong>Average Score:</strong></div>', unsafe_allow_html=True)
            st.markdown(f'<div class="summary-value">{average_score:.2f}</div>', unsafe_allow_html=True)
        
        # Submit button
        submitted = st.form_submit_button(
            "Submit",
            use_container_width=True
        )
        
        if submitted:
            # Convert selectbox values to appropriate types
            part_time_job_bool = part_time_job == "Yes"
            extracurricular_bool = extracurricular_activities == "Yes"
            
            # Get recommendations
            recommendations = get_recommendations(
                scaler, model, gender, part_time_job_bool, absence_days,
                extracurricular_bool, weekly_study_hours,
                math_score, history_score, physics_score,
                chemistry_score, biology_score, english_score,
                geography_score, total_score, average_score
            )
            
            # Display recommendations
            if recommendations:
                st.markdown('<div class="recommendations-header">🎯 Career Recommendations</div>', unsafe_allow_html=True)
                
                for i, (career, probability) in enumerate(recommendations, 1):
                    confidence_percentage = probability * 100
                    
                    st.markdown(f"""
                    <div class="recommendation-item">
                        <div class="rank-number">{i}</div>
                        <div class="career-name">{career}</div>
                        <div class="confidence-score">{confidence_percentage:.1f}%</div>
                    </div>
                    """, unsafe_allow_html=True)
            else:
                st.error("Unable to generate recommendations. Please check your input data.")

if __name__ == "__main__":
    main()