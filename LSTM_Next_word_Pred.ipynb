{"cells": [{"cell_type": "code", "execution_count": 81, "metadata": {"id": "Y7OW1-2xZEGd"}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {"id": "oL6kt5be3X5Y"}, "source": ["# ***Next_word Prediction Experiment***"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xUiZW-uD19lh", "outputId": "fb451a61-1298-4849-9885-e7f9798e2606"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package gutenberg to\n", "[nltk_data]     C:\\Users\\<USER>\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Unzipping corpora\\gutenberg.zip.\n"]}], "source": ["import nltk\n", "nltk.download('gutenberg')\n", "from nltk.corpus import gutenberg\n", "import pandas as pd\n", "# load the dataset\n", "data=gutenberg.raw('shakespeare-hamlet.txt')\n", "# save the dataset\n", "with open('hamlet.txt','w') as file:\n", "  file.write(data)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "39JSQYu119oD"}, "outputs": [], "source": ["import numpy as np\n", "from tensorflow.keras.preprocessing.text import Tokenizer\n", "from tensorflow.keras.preprocessing.sequence import pad_sequences\n", "from sklearn.model_selection import train_test_split\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "h3s75HJu19q-"}, "outputs": [], "source": ["# import dataset in reading mode\n", "with open('hamlet.txt','r') as file:\n", "  text=file.read().lower()\n", "# tokenize and assign integer index to each word\n", "tokenizer_obj=Tokenizer()\n", "tokenizer_obj.fit_on_texts([text])\n", "total_words=len(tokenizer_obj.word_index)+1\n", "total_words=4818"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "AC0yEMUM19uO"}, "outputs": [], "source": ["input_sequences=[]\n", "for line in text.split('\\n'):\n", "  token_list=tokenizer_obj.texts_to_sequences([line])[0]\n", "  for i in range(1,len(token_list)):\n", "    n_gram_seq=token_list[:i+1]\n", "    input_sequences.append(n_gram_seq)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lIkYpz_c19xE", "outputId": "8d44fc8f-c47f-40b2-a32b-275232d5d94b"}, "outputs": [{"data": {"text/plain": ["14"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["max_seq_len=max(len(i) for i in input_sequences)\n", "max_seq_len"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "aaB8aiKH190O"}, "outputs": [], "source": ["input_sequences=np.array(pad_sequences(input_sequences,maxlen=max_seq_len,padding='pre'))\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"id": "OsGxtzHp193X"}, "outputs": [], "source": ["import tensorflow as tf\n", "x,y=input_sequences[:,:-1],input_sequences[:,-1]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"id": "UKtTdpeb197C"}, "outputs": [], "source": ["y=tf.keras.utils.to_categorical(y,num_classes=total_words)\n", "x_train,x_test,y_train,y_test=train_test_split(x,y,test_size=0.2,)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"id": "0w7DoP-U1-FG"}, "outputs": [], "source": ["from tensorflow.keras.callbacks import EarlyStopping\n", "early_stoping=EarlyStopping(monitor='val_loss',patience=7,restore_best_weights=True)\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"id": "LhDghJRR-tf1"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\keras\\src\\layers\\core\\embedding.py:90: UserWarning: Argument `input_length` is deprecated. Just remove it.\n", "  warnings.warn(\n"]}], "source": ["from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.layers import Embedding,LSTM,GRU,Dropout,Dense\n", "from tensorflow.keras.layers import BatchNormalization\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras.regularizers import l2\n", "# define the mode\n", "model=Sequential()\n", "model.add(Embedding(total_words,100,input_length=max_seq_len-1))\n", "model.add(GRU(64,return_sequences=True))\n", "model.add(GRU(32,return_sequences=True))\n", "model.add(GRU(16))\n", "model.add(BatchNormalization())\n", "model.add(Dropout(0.3))\n", "\n", "model.add(Dense(total_words, activation='softmax', kernel_regularizer=l2(0.01)))\n", "optimizer = <PERSON>(learning_rate=0.001)\n", "model.compile(loss=\"categorical_crossentropy\",optimizer=optimizer,metrics=['accuracy'])"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 384}, "id": "-woo43O1-tl7", "outputId": "a2d3de62-8a04-4cfb-b1dd-bf96350dcd09"}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"sequential\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"sequential\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ embedding (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Embedding</span>)           │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">100</span>)        │       <span style=\"color: #00af00; text-decoration-color: #00af00\">481,800</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ gru (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">GRU</span>)                       │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)         │        <span style=\"color: #00af00; text-decoration-color: #00af00\">31,872</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ gru_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">GRU</span>)                     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)         │         <span style=\"color: #00af00; text-decoration-color: #00af00\">9,408</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ gru_2 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">GRU</span>)                     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">16</span>)             │         <span style=\"color: #00af00; text-decoration-color: #00af00\">2,400</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ batch_normalization             │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">16</span>)             │            <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span> │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">BatchNormalization</span>)            │                        │               │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dropout</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">16</span>)             │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">4818</span>)           │        <span style=\"color: #00af00; text-decoration-color: #00af00\">81,906</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ embedding (\u001b[38;5;33mEmbedding\u001b[0m)           │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m100\u001b[0m)        │       \u001b[38;5;34m481,800\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ gru (\u001b[38;5;33mGRU\u001b[0m)                       │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m64\u001b[0m)         │        \u001b[38;5;34m31,872\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ gru_1 (\u001b[38;5;33mGRU\u001b[0m)                     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m32\u001b[0m)         │         \u001b[38;5;34m9,408\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ gru_2 (\u001b[38;5;33mGRU\u001b[0m)                     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m16\u001b[0m)             │         \u001b[38;5;34m2,400\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ batch_normalization             │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m16\u001b[0m)             │            \u001b[38;5;34m64\u001b[0m │\n", "│ (\u001b[38;5;33mBatchNormalization\u001b[0m)            │                        │               │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout (\u001b[38;5;33mDropout\u001b[0m)               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m16\u001b[0m)             │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (\u001b[38;5;33mDense\u001b[0m)                   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m4818\u001b[0m)           │        \u001b[38;5;34m81,906\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">607,450</span> (2.32 MB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m607,450\u001b[0m (2.32 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">607,418</span> (2.32 MB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m607,418\u001b[0m (2.32 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">32</span> (128.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m32\u001b[0m (128.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model.build(input_shape=(None, max_seq_len))\n", "model.summary()\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "T0wngEXo-tp2", "outputId": "20a96b0c-4454-4263-efcb-72f379dd829c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/100\n", "\u001b[1m644/644\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m31s\u001b[0m 34ms/step - accuracy: 0.0257 - loss: 8.2986 - val_accuracy: 0.0495 - val_loss: 6.9536\n", "Epoch 2/100\n", "\u001b[1m644/644\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m36s\u001b[0m 26ms/step - accuracy: 0.0599 - loss: 6.9222 - val_accuracy: 0.0595 - val_loss: 6.8610\n", "Epoch 3/100\n", "\u001b[1m644/644\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m18s\u001b[0m 28ms/step - accuracy: 0.0692 - loss: 6.6261 - val_accuracy: 0.0565 - val_loss: 6.7491\n", "Epoch 4/100\n", "\u001b[1m644/644\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m17s\u001b[0m 27ms/step - accuracy: 0.0811 - loss: 6.4050 - val_accuracy: 0.0657 - val_loss: 6.7338\n", "Epoch 5/100\n", "\u001b[1m644/644\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m21s\u001b[0m 27ms/step - accuracy: 0.0895 - loss: 6.2545 - val_accuracy: 0.0659 - val_loss: 6.7397\n", "Epoch 6/100\n", "\u001b[1m644/644\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m18s\u001b[0m 28ms/step - accuracy: 0.0924 - loss: 6.1531 - val_accuracy: 0.0674 - val_loss: 6.7819\n", "Epoch 7/100\n", "\u001b[1m644/644\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m19s\u001b[0m 29ms/step - accuracy: 0.1092 - loss: 6.0313 - val_accuracy: 0.0661 - val_loss: 6.8153\n", "Epoch 8/100\n", "\u001b[1m644/644\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m19s\u001b[0m 29ms/step - accuracy: 0.1091 - loss: 5.9618 - val_accuracy: 0.0643 - val_loss: 6.8788\n", "Epoch 9/100\n", "\u001b[1m644/644\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m19s\u001b[0m 30ms/step - accuracy: 0.1188 - loss: 5.8917 - val_accuracy: 0.0635 - val_loss: 6.9521\n", "Epoch 10/100\n", "\u001b[1m644/644\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m20s\u001b[0m 29ms/step - accuracy: 0.1254 - loss: 5.7962 - val_accuracy: 0.0633 - val_loss: 7.0295\n", "Epoch 11/100\n", "\u001b[1m644/644\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m19s\u001b[0m 30ms/step - accuracy: 0.1348 - loss: 5.7121 - val_accuracy: 0.0647 - val_loss: 7.0866\n"]}, {"data": {"text/plain": ["<keras.src.callbacks.history.History at 0x2287e067170>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["model.fit(x_train,y_train,epochs=100,validation_data=(x_test,y_test),verbose=1,callbacks=[early_stoping])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "s3x6PMZexovu"}, "outputs": [], "source": ["def Next_word_pred(tokenizer_obj,text,max_seq_len,model):\n", "  token_list=tokenizer_obj.texts_to_sequences([text])[0]\n", "  if len(token_list)>=max_seq_len:\n", "    token_list=token_list[-(max_seq_len-1):]\n", "  token_list=pad_sequences([token_list],maxlen=max_seq_len-1,padding='pre')\n", "  predicted=model.predict(token_list,verbose=0)\n", "  predicted_next_word=np.argsort()[-top_k:][::-1]\n", "  for word,index in tokenizer_obj.word_index.items():\n", "    if index==predicted_next_word:\n", "      return word\n", "  return None"]}, {"cell_type": "code", "execution_count": 80, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 36}, "id": "xiuhywVF8_XY", "outputId": "4dce61b5-e0c9-471b-c11e-8a9f8401851e"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'the'"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["input_text=\"the course fee for  Data Science Mentorship \"\n", "max_seq_len=model.input_shape[1]\n", "next_word=Next_word_pred(tokenizer_obj,input_text,max_seq_len,model)\n", "next_word"]}, {"cell_type": "code", "execution_count": 81, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 36}, "id": "QyiJl4aC8_bJ", "outputId": "1a66e711-7199-4e3a-9ab9-6baf2d5f534d"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'the'"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["next_word"]}, {"cell_type": "code", "execution_count": 81, "metadata": {"id": "HxwmOB9_8_cj"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 81, "metadata": {"id": "4CMf07Ga8_fl"}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 0}