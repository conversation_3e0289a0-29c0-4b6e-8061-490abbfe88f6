import streamlit as st

def apply_custom_styles():
    """Apply custom CSS styles to the Streamlit app"""
    
    st.markdown("""
    <style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Main app styling */
    .main {
        font-family: 'Inter', sans-serif;
        background-color: #f8f9fa;
        padding: 1rem;
    }
    
    /* Remove default Streamlit styling */
    .stApp {
        background-color: #f8f9fa;
    }
    
    /* Header styling */
    .main-header {
        text-align: center;
        padding: 2rem 0;
        background: white;
        border-radius: 12px;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }
    
    .main-header h1 {
        color: #2c3e50;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .main-header p {
        color: #6c757d;
        font-size: 1.1rem;
        font-weight: 400;
        margin: 0;
    }
    
    /* Form styling */
    .stForm {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
        margin-bottom: 2rem;
    }
    
    /* Input field styling */
    .stSelectbox > label {
        font-weight: 500 !important;
        color: #495057 !important;
        font-size: 0.95rem !important;
        margin-bottom: 0.5rem !important;
    }
    
    .stNumberInput > label {
        font-weight: 500 !important;
        color: #495057 !important;
        font-size: 0.95rem !important;
        margin-bottom: 0.5rem !important;
    }
    
    .stSelectbox > div > div {
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 6px;
        font-size: 0.95rem;
    }
    
    .stNumberInput > div > div > input {
        background-color: #fff;
        border: 1px solid #ced4da;
        border-radius: 6px;
        font-size: 0.95rem;
        padding: 0.5rem 0.75rem;
    }
    
    .stNumberInput > div > div > input:focus,
    .stSelectbox > div > div:focus-within {
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
    
    /* Divider */
    .divider {
        height: 1px;
        background: #e9ecef;
        margin: 2rem 0 1rem 0;
    }
    
    /* Summary fields */
    .summary-field {
        font-weight: 500;
        color: #495057;
        font-size: 0.95rem;
        margin-bottom: 0.5rem;
    }
    
    .summary-value {
        background: #f8f9fa;
        padding: 0.75rem;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        font-size: 1rem;
        font-weight: 500;
        color: #495057;
        margin-bottom: 1rem;
    }
    
    /* Button styling */
    .stFormSubmitButton > button {
        background: #007bff;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 0.75rem 2rem;
        font-size: 1rem;
        font-weight: 500;
        transition: background-color 0.2s ease;
        margin-top: 1rem;
    }
    
    .stFormSubmitButton > button:hover {
        background: #0056b3;
    }
    
    /* Recommendations styling */
    .recommendations-header {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin: 2rem 0 1rem 0;
        padding: 1rem;
        background: white;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }
    
    .recommendation-item {
        display: flex;
        align-items: center;
        background: white;
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        border: 1px solid #e9ecef;
    }
    
    .rank-number {
        background: #007bff;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 0.9rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }
    
    .career-name {
        flex-grow: 1;
        font-weight: 500;
        color: #2c3e50;
        font-size: 1rem;
    }
    
    .confidence-score {
        font-weight: 600;
        color: #007bff;
        font-size: 0.95rem;
    }
    
    /* Hide Streamlit branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    .stDeployButton {display: none;}
    
    /* Responsive design */
    @media (max-width: 768px) {
        .main-header h1 {
            font-size: 2rem;
        }
        
        .main-header p {
            font-size: 1rem;
        }
        
        .stForm {
            padding: 1rem;
        }
    }
    </style>
    """, unsafe_allow_html=True)