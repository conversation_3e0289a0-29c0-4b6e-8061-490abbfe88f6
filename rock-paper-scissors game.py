import cv2
import mediapipe as mp
import numpy as np
import random
import time
from collections import Counter

class RockPaperScissorsGame:
    def __init__(self):
        # Initialize MediaPipe
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=1,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_draw = mp.solutions.drawing_utils
        
        # Game variables
        self.player_score = 0
        self.computer_score = 0
        self.game_state = "waiting"  # waiting, countdown, playing, result
        self.countdown_start = 0
        self.result_start = 0
        self.player_choice = ""
        self.computer_choice = ""
        self.winner = ""
        self.gesture_history = []
        
        # Colors (BGR format)
        self.colors = {
            'green': (0, 255, 0),
            'red': (0, 0, 255),
            'blue': (255, 0, 0),
            'yellow': (0, 255, 255),
            'white': (255, 255, 255),
            'black': (0, 0, 0),
            'purple': (255, 0, 255),
            'orange': (0, 165, 255)
        }
        
    def detect_gesture(self, landmarks):
        """Detect rock, paper, or scissors gesture from hand landmarks"""
        if not landmarks:
            return "unknown"
            
        # Get landmark positions
        thumb_tip = landmarks[4]
        thumb_ip = landmarks[3]
        index_tip = landmarks[8]
        index_pip = landmarks[6]
        middle_tip = landmarks[12]
        middle_pip = landmarks[10]
        ring_tip = landmarks[16]
        ring_pip = landmarks[14]
        pinky_tip = landmarks[20]
        pinky_pip = landmarks[18]
        
        # Count extended fingers
        fingers_up = []
        
        # Thumb (special case - check if tip is to the right/left of IP joint)
        if thumb_tip.x > thumb_ip.x:  # Right hand
            fingers_up.append(thumb_tip.x > thumb_ip.x)
        else:  # Left hand
            fingers_up.append(thumb_tip.x < thumb_ip.x)
            
        # Other fingers (check if tip is above PIP joint)
        fingers_up.append(index_tip.y < index_pip.y)
        fingers_up.append(middle_tip.y < middle_pip.y)
        fingers_up.append(ring_tip.y < ring_pip.y)
        fingers_up.append(pinky_tip.y < pinky_pip.y)
        
        fingers_count = sum(fingers_up)
        
        # Gesture recognition logic
        if fingers_count == 0:
            return "rock"
        elif fingers_count == 5:
            return "paper"
        elif fingers_count == 2 and fingers_up[1] and fingers_up[2]:
            return "scissors"
        else:
            return "unknown"
    
    def get_computer_choice(self):
        """Generate computer's choice"""
        choices = ["rock", "paper", "scissors"]
        return random.choice(choices)
    
    def determine_winner(self, player, computer):
        """Determine the winner of the round"""
        if player == computer:
            return "tie"
        elif (player == "rock" and computer == "scissors") or \
             (player == "paper" and computer == "rock") or \
             (player == "scissors" and computer == "paper"):
            return "player"
        else:
            return "computer"
    
    def draw_gesture_icon(self, frame, gesture, position, size=100):
        """Draw gesture icon on frame"""
        x, y = position
        
        if gesture == "rock":
            # Draw a fist
            cv2.circle(frame, (x + size//2, y + size//2), size//3, self.colors['white'], -1)
            cv2.circle(frame, (x + size//2, y + size//2), size//3, self.colors['black'], 3)
            cv2.putText(frame, "✊", (x + size//4, y + 3*size//4), cv2.FONT_HERSHEY_SIMPLEX, 2, self.colors['white'], 3)
            
        elif gesture == "paper":
            # Draw an open hand
            cv2.rectangle(frame, (x, y), (x + size, y + size), self.colors['white'], -1)
            cv2.rectangle(frame, (x, y), (x + size, y + size), self.colors['black'], 3)
            cv2.putText(frame, "✋", (x + size//4, y + 3*size//4), cv2.FONT_HERSHEY_SIMPLEX, 2, self.colors['black'], 3)
            
        elif gesture == "scissors":
            # Draw scissors
            cv2.rectangle(frame, (x, y), (x + size, y + size), self.colors['white'], -1)
            cv2.rectangle(frame, (x, y), (x + size, y + size), self.colors['black'], 3)
            cv2.putText(frame, "✌️", (x + size//4, y + 3*size//4), cv2.FONT_HERSHEY_SIMPLEX, 2, self.colors['black'], 3)
    
    def draw_ui(self, frame):
        """Draw the game UI"""
        height, width = frame.shape[:2]
        
        # Draw title
        cv2.putText(frame, "ROCK PAPER SCISSORS", (width//2 - 200, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, self.colors['yellow'], 3)
        
        # Draw scores
        cv2.putText(frame, f"Player: {self.player_score}", (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, self.colors['green'], 2)
        cv2.putText(frame, f"Computer: {self.computer_score}", (width - 200, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, self.colors['red'], 2)
        
        # Draw instructions based on game state
        if self.game_state == "waiting":
            cv2.putText(frame, "Show your hand gesture to start!", 
                       (width//2 - 200, height - 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, self.colors['white'], 2)
            cv2.putText(frame, "Rock ✊  Paper ✋  Scissors ✌️", 
                       (width//2 - 180, height - 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, self.colors['white'], 2)
                       
        elif self.game_state == "countdown":
            countdown = 3 - int(time.time() - self.countdown_start)
            if countdown > 0:
                cv2.putText(frame, str(countdown), (width//2 - 30, height//2), 
                           cv2.FONT_HERSHEY_SIMPLEX, 4, self.colors['yellow'], 8)
            else:
                cv2.putText(frame, "SHOW!", (width//2 - 80, height//2), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2, self.colors['red'], 4)
                           
        elif self.game_state == "result":
            # Draw choices
            cv2.putText(frame, "Player:", (100, height//2 - 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, self.colors['white'], 2)
            self.draw_gesture_icon(frame, self.player_choice, (100, height//2 - 50))
            
            cv2.putText(frame, "Computer:", (width - 250, height//2 - 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, self.colors['white'], 2)
            self.draw_gesture_icon(frame, self.computer_choice, (width - 200, height//2 - 50))
            
            # Draw result
            if self.winner == "tie":
                cv2.putText(frame, "TIE!", (width//2 - 50, height//2 + 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2, self.colors['yellow'], 4)
            elif self.winner == "player":
                cv2.putText(frame, "YOU WIN!", (width//2 - 100, height//2 + 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2, self.colors['green'], 4)
            else:
                cv2.putText(frame, "YOU LOSE!", (width//2 - 110, height//2 + 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2, self.colors['red'], 4)
    
    def process_frame(self, frame):
        """Process each frame from the camera"""
        # Flip frame horizontally for mirror effect
        frame = cv2.flip(frame, 1)
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Process frame with MediaPipe
        results = self.hands.process(rgb_frame)
        
        current_gesture = "unknown"
        
        # Draw hand landmarks and detect gesture
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                # Draw landmarks
                self.mp_draw.draw_landmarks(frame, hand_landmarks, self.mp_hands.HAND_CONNECTIONS)
                
                # Detect gesture
                current_gesture = self.detect_gesture(hand_landmarks.landmark)
                
                # Display detected gesture
                cv2.putText(frame, f"Detected: {current_gesture.upper()}", 
                           (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.8, self.colors['blue'], 2)
        
        # Game logic
        current_time = time.time()
        
        if self.game_state == "waiting":
            if current_gesture in ["rock", "paper", "scissors"]:
                self.game_state = "countdown"
                self.countdown_start = current_time
                self.gesture_history = []
                
        elif self.game_state == "countdown":
            if current_time - self.countdown_start >= 4:  # 3 second countdown + 1 second "SHOW!"
                # Get most common gesture during the "SHOW!" period
                if self.gesture_history:
                    most_common = Counter(self.gesture_history).most_common(1)[0][0]
                    if most_common in ["rock", "paper", "scissors"]:
                        self.player_choice = most_common
                        self.computer_choice = self.get_computer_choice()
                        self.winner = self.determine_winner(self.player_choice, self.computer_choice)
                        
                        # Update scores
                        if self.winner == "player":
                            self.player_score += 1
                        elif self.winner == "computer":
                            self.computer_score += 1
                        
                        self.game_state = "result"
                        self.result_start = current_time
                    else:
                        self.game_state = "waiting"
                else:
                    self.game_state = "waiting"
            elif current_time - self.countdown_start >= 3:  # During "SHOW!" period
                if current_gesture in ["rock", "paper", "scissors"]:
                    self.gesture_history.append(current_gesture)
                    
        elif self.game_state == "result":
            if current_time - self.result_start >= 3:  # Show result for 3 seconds
                self.game_state = "waiting"
        
        # Draw UI
        self.draw_ui(frame)
        
        return frame
    
    def run(self):
        """Run the game"""
        cap = cv2.VideoCapture(0)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        print("🎮 Rock Paper Scissors CV Game Started!")
        print("📋 Instructions:")
        print("   - Show your hand gesture (rock ✊, paper ✋, or scissors ✌️)")
        print("   - Game will automatically start countdown")
        print("   - Make your gesture when 'SHOW!' appears")
        print("   - Press 'q' to quit, 'r' to reset scores")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Process frame
            frame = self.process_frame(frame)
            
            # Display frame
            cv2.imshow("Rock Paper Scissors - Computer Vision", frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('r'):
                self.player_score = 0
                self.computer_score = 0
                self.game_state = "waiting"
                print("🔄 Scores reset!")
        
        cap.release()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    # Create and run the game
    game = RockPaperScissorsGame()
    game.run()