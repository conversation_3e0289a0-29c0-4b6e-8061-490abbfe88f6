import pickle
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
import os

# Career class names mapping
CLASS_NAMES = [
    'Lawyer', 'Doctor', 'Government Officer', 'Artist', 'Unknown',
    'Software Engineer', 'Teacher', 'Business Owner', 'Scientist',
    'Banker', 'Writer', 'Accountant', 'Designer',
    'Construction Engineer', 'Game Developer', 'Stock Investor',
    'Real Estate Developer'
]

def load_models():
    """Load the trained scaler and model from pickle files"""
    try:
        scaler = pickle.load(open("Models/scaler.pkl", 'rb'))
        model = pickle.load(open("Models/model.pkl", 'rb'))
        return scaler, model
    except FileNotFoundError as e:
        raise Exception(f"Model files not found. Please ensure 'Models/scaler.pkl' and 'Models/model.pkl' exist. Error: {e}")
    except Exception as e:
        raise Exception(f"Error loading models: {e}")

def get_recommendations(scaler, model, gender, part_time_job, absence_days, 
                       extracurricular_activities, weekly_self_study_hours,
                       math_score, history_score, physics_score, chemistry_score,
                       biology_score, english_score, geography_score, total_score, average_score):
    """Get career recommendations based on student data"""
    
    # Encode categorical variables
    gender_encoded = 1 if gender.lower() == 'female' else 0
    part_time_job_encoded = 1 if part_time_job else 0
    extracurricular_activities_encoded = 1 if extracurricular_activities else 0
    
    # Create feature array
    feature_array = np.array([[
        gender_encoded, part_time_job_encoded, absence_days, extracurricular_activities_encoded,
        weekly_self_study_hours, math_score, history_score, physics_score,
        chemistry_score, biology_score, english_score, geography_score, total_score, average_score
    ]])
    
    # Scale features
    scaled_features = scaler.transform(feature_array)
    
    # Predict using the model
    probabilities = model.predict_proba(scaled_features)
    
    # Get top five predicted classes along with their probabilities
    top_classes_idx = np.argsort(-probabilities[0])[:5]
    top_classes_names_probs = [(CLASS_NAMES[idx], probabilities[0][idx]) for idx in top_classes_idx]
    
    return top_classes_names_probs

def create_radar_chart(math_score, history_score, physics_score, chemistry_score,
                      biology_score, english_score, geography_score):
    """Create a radar chart for subject scores"""
    
    subjects = ['Math', 'History', 'Physics', 'Chemistry', 'Biology', 'English', 'Geography']
    scores = [math_score, history_score, physics_score, chemistry_score, biology_score, english_score, geography_score]
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatterpolar(
        r=scores,
        theta=subjects,
        fill='toself',
        name='Scores',
        fillcolor='rgba(102, 126, 234, 0.3)',
        line=dict(color='rgba(102, 126, 234, 1)', width=2),
        marker=dict(color='rgba(102, 126, 234, 1)', size=8)
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100],
                tickfont=dict(size=10),
                gridcolor='rgba(0, 0, 0, 0.1)'
            ),
            angularaxis=dict(
                tickfont=dict(size=12, color='#2d3748')
            )
        ),
        showlegend=False,
        title=dict(
            text="Subject Performance Overview",
            x=0.5,
            font=dict(size=16, color='#2d3748')
        ),
        font=dict(family="Poppins"),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        height=400
    )
    
    return fig

def create_score_chart(math_score, history_score, physics_score, chemistry_score,
                      biology_score, english_score, geography_score):
    """Create a bar chart for individual subject scores"""
    
    subjects = ['Math', 'History', 'Physics', 'Chemistry', 'Biology', 'English', 'Geography']
    scores = [math_score, history_score, physics_score, chemistry_score, biology_score, english_score, geography_score]
    
    # Color coding based on performance
    colors = []
    for score in scores:
        if score >= 90:
            colors.append('#22c55e')  # Green for excellent
        elif score >= 80:
            colors.append('#3b82f6')  # Blue for good
        elif score >= 70:
            colors.append('#f59e0b')  # Orange for average
        else:
            colors.append('#f87171')  # Red for needs improvement
    
    fig = go.Figure(data=[
        go.Bar(
            x=subjects,
            y=scores,
            marker_color=colors,
            text=scores,
            textposition='auto',
            textfont=dict(color='white', size=12, family='Poppins'),
            hovertemplate='<b>%{x}</b><br>Score: %{y}<extra></extra>'
        )
    ])
    
    fig.update_layout(
        title=dict(
            text="Individual Subject Scores",
            x=0.5,
            font=dict(size=16, color='#2d3748')
        ),
        xaxis=dict(
            title="Subjects",
            tickfont=dict(size=10, color='#2d3748'),
            titlefont=dict(size=12, color='#2d3748')
        ),
        yaxis=dict(
            title="Score",
            range=[0, 100],
            tickfont=dict(size=10, color='#2d3748'),
            titlefont=dict(size=12, color='#2d3748')
        ),
        font=dict(family="Poppins"),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        height=350,
        showlegend=False
    )
    
    # Add grid lines
    fig.update_yaxis(showgrid=True, gridwidth=1, gridcolor='rgba(0,0,0,0.1)')
    fig.update_xaxis(showgrid=False)
    
    return fig

def get_career_insights(recommendations):
    """Generate insights based on career recommendations"""
    
    insights = []
    top_career = recommendations[0][0]
    top_probability = recommendations[0][1] * 100
    
    # Career-specific insights
    career_tips = {
        'Software Engineer': {
            'skills': ['Programming', 'Problem Solving', 'Mathematics', 'Logic'],
            'advice': 'Focus on strengthening your math and physics scores. Consider learning programming languages.'
        },
        'Doctor': {
            'skills': ['Biology', 'Chemistry', 'Physics', 'Communication'],
            'advice': 'Excel in sciences, especially biology and chemistry. Develop strong communication skills.'
        },
        'Lawyer': {
            'skills': ['English', 'History', 'Critical Thinking', 'Communication'],
            'advice': 'Strengthen your English and history scores. Practice debate and critical analysis.'
        },
        'Teacher': {
            'skills': ['Communication', 'Subject Knowledge', 'Patience', 'Leadership'],
            'advice': 'Maintain good scores across subjects. Develop leadership and communication skills.'
        },
        'Artist': {
            'skills': ['Creativity', 'Visual Arts', 'Expression', 'Innovation'],
            'advice': 'Focus on creative subjects and develop your artistic portfolio.'
        },
        'Scientist': {
            'skills': ['Research', 'Mathematics', 'Physics', 'Chemistry', 'Biology'],
            'advice': 'Excel in science subjects. Develop research and analytical thinking skills.'
        },
        'Business Owner': {
            'skills': ['Leadership', 'Mathematics', 'Communication', 'Economics'],
            'advice': 'Develop leadership skills and understand basic economics and mathematics.'
        }
    }
    
    if top_career in career_tips:
        return career_tips[top_career]
    else:
        return {
            'skills': ['Communication', 'Problem Solving', 'Dedication', 'Learning'],
            'advice': 'Continue developing your academic skills and explore your interests.'
        }

def calculate_subject_strengths(math_score, history_score, physics_score, chemistry_score,
                              biology_score, english_score, geography_score):
    """Calculate subject area strengths"""
    
    scores = {
        'Math': math_score,
        'History': history_score,
        'Physics': physics_score,
        'Chemistry': chemistry_score,
        'Biology': biology_score,
        'English': english_score,
        'Geography': geography_score
    }
    
    # Group subjects by area
    areas = {
        'STEM': (math_score + physics_score + chemistry_score) / 3,
        'Sciences': (physics_score + chemistry_score + biology_score) / 3,
        'Humanities': (history_score + english_score + geography_score) / 3,
        'Analytical': (math_score + physics_score) / 2,
        'Communication': english_score
    }
    
    strongest_area = max(areas, key=areas.get)
    strongest_score = areas[strongest_area]
    
    return strongest_area, strongest_score, areas

def format_percentage(value):
    """Format probability as percentage"""
    return f"{value * 100:.1f}%"

def get_performance_level(average_score):
    """Get performance level description"""
    if average_score >= 90:
        return "Exceptional", "🌟"
    elif average_score >= 80:
        return "Excellent", "🎯"
    elif average_score >= 70:
        return "Good", "👍"
    elif average_score >= 60:
        return "Satisfactory", "📈"
    else:
        return "Needs Improvement", "💪"

def create_comparison_chart(user_scores, career_requirements):
    """Create a comparison chart between user scores and career requirements"""
    
    subjects = list(user_scores.keys())
    user_values = list(user_scores.values())
    required_values = [career_requirements.get(subject, 70) for subject in subjects]
    
    fig = go.Figure()
    
    fig.add_trace(go.Bar(
        name='Your Scores',
        x=subjects,
        y=user_values,
        marker_color='rgba(102, 126, 234, 0.8)'
    ))
    
    fig.add_trace(go.Bar(
        name='Recommended Level',
        x=subjects,
        y=required_values,
        marker_color='rgba(255, 99, 132, 0.7)'
    ))

    return fig