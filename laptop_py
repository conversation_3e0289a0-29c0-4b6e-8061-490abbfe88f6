import streamlit as st
import pandas as pd
import numpy as np
import pickle

# Set page config
st.set_page_config(
    page_title="Laptop Price Predictor",
    page_icon="💻",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: bold;
    }
    
    .sub-header {
        font-size: 1.5rem;
        color: #444;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .prediction-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        color: white;
        margin: 2rem 0;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }
    
    .prediction-price {
        font-size: 3rem;
        font-weight: bold;
        margin: 1rem 0;
    }
    
    .input-section {
        background: #f8f9fa;
        padding: 2rem;
        border-radius: 10px;
        margin: 1rem 0;
        border-left: 4px solid #1f77b4;
    }
    
    .stSelectbox > div > div {
        background-color: white;
        border-radius: 8px;
    }
    
    .stSlider > div > div > div {
        background-color: #1f77b4;
    }
    
    .feature-importance {
        background: #fff;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Load the model and data
@st.cache_data
def load_model():
    try:
        df = pickle.load(open('df.pkl', 'rb'))
        pipe = pickle.load(open('pipe.pkl', 'rb'))
        return df, pipe
    except:
        st.error("⚠️ Model files not found. Please ensure 'df.pkl' and 'pipe.pkl' are in the same directory.")
        return None, None

# Header
st.markdown('<h1 class="main-header">💻 Laptop Price Predictor</h1>', unsafe_allow_html=True)
st.markdown('<p class="sub-header">Get instant price predictions for laptops based on specifications</p>', unsafe_allow_html=True)

# Load model
df, pipe = load_model()

if df is not None and pipe is not None:
    # Create two columns for layout
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown('<div class="input-section">', unsafe_allow_html=True)
        st.markdown("### 🔧 Basic Specifications")
        
        # Company selection
        company = st.selectbox(
            "💼 Brand",
            df['Company'].unique(),
            help="Select the laptop manufacturer"
        )
        
        # Type selection
        type_name = st.selectbox(
            "📱 Type",
            df['TypeName'].unique(),
            help="Select the laptop category"
        )
        
        # RAM selection
        ram = st.selectbox(
            "🎯 RAM (GB)",
            sorted(df['Ram'].unique()),
            help="Select RAM capacity"
        )
        
        # Weight slider
        weight = st.slider(
            "⚖️ Weight (kg)",
            min_value=0.5,
            max_value=5.0,
            value=2.0,
            step=0.1,
            help="Laptop weight in kilograms"
        )
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        st.markdown('<div class="input-section">', unsafe_allow_html=True)
        st.markdown("### 🖥️ Display Features")
        
        # Touchscreen
        touchscreen = st.radio(
            "👆 Touchscreen",
            [0, 1],
            format_func=lambda x: "Yes" if x == 1 else "No",
            horizontal=True
        )
        
        # IPS Panel
        ips = st.radio(
            "🎨 IPS Panel",
            [0, 1],
            format_func=lambda x: "Yes" if x == 1 else "No",
            horizontal=True
        )
        
        # PPI slider
        ppi = st.slider(
            "📏 PPI (Pixels per inch)",
            min_value=50,
            max_value=400,
            value=150,
            step=10,
            help="Screen pixel density"
        )
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="input-section">', unsafe_allow_html=True)
        st.markdown("### 💾 Storage Configuration")
        
        # HDD capacity
        hdd = st.number_input(
            "💽 HDD (GB)",
            min_value=0,
            max_value=2000,
            value=0,
            step=250,
            help="Hard Disk Drive capacity"
        )
        
        # SSD capacity
        ssd = st.number_input(
            "⚡ SSD (GB)",
            min_value=0,
            max_value=2000,
            value=256,
            step=128,
            help="Solid State Drive capacity"
        )
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        st.markdown('<div class="input-section">', unsafe_allow_html=True)
        st.markdown("### 🔧 Hardware Configuration")
        
        # CPU brand
        cpu_brand = st.selectbox(
            "🔲 CPU Brand",
            df['Cpu brand'].unique(),
            help="Select processor brand"
        )
        
        # GPU brand
        gpu_brand = st.selectbox(
            "🎮 GPU Brand",
            df['Gpu brand'].unique(),
            help="Select graphics card brand"
        )
        
        # Operating System
        os = st.selectbox(
            "💿 Operating System",
            df['os'].unique(),
            help="Select operating system"
        )
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Prediction button
    st.markdown("<br>", unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        if st.button("🚀 Predict Price", use_container_width=True):
            # Create input dataframe
            input_data = pd.DataFrame({
                'Company': [company],
                'TypeName': [type_name],
                'Ram': [ram],
                'Weight': [weight],
                'Touchscreen': [touchscreen],
                'Ips': [ips],
                'ppi': [ppi],
                'Cpu brand': [cpu_brand],
                'HDD': [hdd],
                'SSD': [ssd],
                'Gpu brand': [gpu_brand],
                'os': [os]
            })
            
            # Make prediction
            try:
                prediction = pipe.predict(input_data)[0]
                price = np.exp(prediction)
                
                # Display prediction
                st.markdown(f'''
                <div class="prediction-box">
                    <h2>💰 Predicted Price</h2>
                    <div class="prediction-price">₹{price:,.0f}</div>
                    <p>Based on the specifications you provided</p>
                </div>
                ''', unsafe_allow_html=True)
                
                # Price insights
                st.markdown("### 📊 Price Insights")
                
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("💡 Price Range", f"₹{price*0.9:,.0f} - ₹{price*1.1:,.0f}")
                
                with col2:
                    avg_price = df['Price'].mean()
                    diff = price - avg_price
                    st.metric("📈 vs Average", f"₹{avg_price:,.0f}", f"₹{diff:+,.0f}")
                
                with col3:
                    price_category = "Budget" if price < 40000 else "Mid-range" if price < 80000 else "Premium"
                    st.metric("🏷️ Category", price_category)
                
            except Exception as e:
                st.error(f"⚠️ Error making prediction: {str(e)}")
    
    # Additional information
    st.markdown("---")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 💡 Tips for Better Predictions
        - **RAM**: Higher RAM generally increases price
        - **Storage**: SSDs are more expensive than HDDs
        - **Brand**: Premium brands command higher prices
        - **Display**: Touchscreen and IPS panels add to cost
        - **Processor**: Latest generation CPUs cost more
        """)
    
    with col2:
        st.markdown("""
        ### 📈 Model Performance
        - **Algorithm**: Random Forest Regressor
        - **Accuracy**: High R² score on test data
        - **Features**: 12 key laptop specifications
        - **Training Data**: 1000+ laptop configurations
        - **Last Updated**: Recently trained model
        """)

else:
    st.markdown("""
    ### 🚨 Setup Required
    
    To use this application, you need to have the following files in the same directory:
    - `df.pkl` - The preprocessed dataset
    - `pipe.pkl` - The trained machine learning model
    
    Please run your training script first to generate these files.
    """)

# Footer
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #666; margin-top: 2rem;">
    <p>Built with ❤️ using Streamlit | Laptop Price Predictor v1.0</p>
</div>
""", unsafe_allow_html=True)