#!/usr/bin/env python3
"""
Laptop Price Predictor Model Retraining Script
This script retrains the model to fix compatibility issues with current scikit-learn version.
"""

import pandas as pd
import numpy as np
import pickle
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor
from sklearn.preprocessing import LabelEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.metrics import r2_score, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

def preprocess_data():
    """Load and preprocess the laptop data"""
    print("📊 Loading and preprocessing data...")
    
    # Load the dataset
    df = pd.read_csv('laptop_data.csv')
    
    # Drop unnecessary columns
    if 'Unnamed: 0' in df.columns:
        df.drop(columns=['Unnamed: 0'], inplace=True)
    
    # Clean RAM and Weight columns
    df['Ram'] = df['Ram'].str.replace('GB', '').astype('int32')
    df['Weight'] = df['Weight'].str.replace('kg', '').astype('float32')
    
    # Extract screen resolution and calculate PPI
    df['X_res'] = df['ScreenResolution'].str.extract('(\d+)x').astype('int')
    df['Y_res'] = df['ScreenResolution'].str.extract('x(\d+)').astype('int')
    df['ppi'] = (((df['X_res']**2) + (df['Y_res']**2))**0.5 / df['Inches']).astype('float')
    
    # Extract touchscreen and IPS information
    df['Touchscreen'] = df['ScreenResolution'].str.contains('Touchscreen').astype(int)
    df['Ips'] = df['ScreenResolution'].str.contains('IPS').astype(int)
    
    # Extract CPU brand
    df['Cpu brand'] = df['Cpu'].str.split().str[0]
    
    # Extract GPU brand
    df['Gpu brand'] = df['Gpu'].str.split().str[0]
    
    # Extract storage information
    df['SSD'] = 0
    df['HDD'] = 0
    df['Hybrid'] = 0
    df['Flash_Storage'] = 0
    
    for index, row in df.iterrows():
        memory = row['Memory']
        if 'SSD' in memory:
            df.loc[index, 'SSD'] = int(memory.split('SSD')[0].split()[-1].replace('GB', '').replace('TB', '000'))
        if 'HDD' in memory:
            df.loc[index, 'HDD'] = int(memory.split('HDD')[0].split()[-1].replace('GB', '').replace('TB', '000'))
        if 'Hybrid' in memory:
            df.loc[index, 'Hybrid'] = int(memory.split('Hybrid')[0].split()[-1].replace('GB', '').replace('TB', '000'))
        if 'Flash Storage' in memory:
            df.loc[index, 'Flash_Storage'] = int(memory.split('Flash Storage')[0].split()[-1].replace('GB', '').replace('TB', '000'))
    
    # Clean up operating system
    df['os'] = df['OpSys'].replace({
        'Windows 10': 'Windows',
        'Windows 7': 'Windows',
        'Windows 10 S': 'Windows',
        'macOS': 'Mac',
        'Mac OS X': 'Mac',
        'Linux': 'Linux',
        'Android': 'Others',
        'Chrome OS': 'Others',
        'No OS': 'Others'
    })
    
    # Select final features
    final_df = df[['Company', 'TypeName', 'Ram', 'Weight', 'Touchscreen', 'Ips', 'ppi', 
                   'Cpu brand', 'HDD', 'SSD', 'Gpu brand', 'os', 'Price']].copy()
    
    # Remove outliers
    final_df = final_df[final_df['Price'] < 300000]
    
    print(f"✅ Data preprocessing complete. Shape: {final_df.shape}")
    return final_df

def create_pipeline():
    """Create the machine learning pipeline"""
    print("🔧 Creating ML pipeline...")
    
    # Use RandomForestRegressor instead of ExtraTreesRegressor for better compatibility
    model = RandomForestRegressor(
        n_estimators=100,
        random_state=42,
        max_depth=15,
        min_samples_split=5,
        min_samples_leaf=2,
        n_jobs=-1
    )
    
    return model

def train_model():
    """Train the laptop price prediction model"""
    print("🚀 Starting model training...")
    
    # Load and preprocess data
    df = preprocess_data()
    
    # Prepare features and target
    X = df.drop('Price', axis=1)
    y = np.log(df['Price'])  # Log transform for better prediction
    
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Create and train the model
    model = create_pipeline()
    
    # Encode categorical variables
    categorical_features = ['Company', 'TypeName', 'Cpu brand', 'Gpu brand', 'os']
    
    # Create label encoders
    label_encoders = {}
    X_train_encoded = X_train.copy()
    X_test_encoded = X_test.copy()
    
    for feature in categorical_features:
        le = LabelEncoder()
        X_train_encoded[feature] = le.fit_transform(X_train[feature])
        X_test_encoded[feature] = le.transform(X_test[feature])
        label_encoders[feature] = le
    
    # Train the model
    print("🎯 Training model...")
    model.fit(X_train_encoded, y_train)
    
    # Make predictions
    y_pred = model.predict(X_test_encoded)
    
    # Calculate metrics
    r2 = r2_score(y_test, y_pred)
    mae = mean_absolute_error(np.exp(y_test), np.exp(y_pred))
    
    print(f"📈 Model Performance:")
    print(f"   R² Score: {r2:.4f}")
    print(f"   Mean Absolute Error: ₹{mae:,.0f}")
    
    # Create pipeline for prediction
    class LaptopPricePipeline:
        def __init__(self, model, label_encoders, feature_columns):
            self.model = model
            self.label_encoders = label_encoders
            self.feature_columns = feature_columns
        
        def predict(self, X):
            X_encoded = X.copy()
            for feature, encoder in self.label_encoders.items():
                if feature in X_encoded.columns:
                    X_encoded[feature] = encoder.transform(X_encoded[feature])
            return self.model.predict(X_encoded)
    
    # Create the pipeline
    pipeline = LaptopPricePipeline(model, label_encoders, X.columns.tolist())
    
    # Save the model and data
    print("💾 Saving model and data...")
    
    # Save the dataframe (for UI dropdowns)
    with open('df.pkl', 'wb') as f:
        pickle.dump(df, f)
    
    # Save the pipeline
    with open('pipe.pkl', 'wb') as f:
        pickle.dump(pipeline, f)
    
    print("✅ Model training complete!")
    print("📁 Files saved:")
    print("   - df.pkl (preprocessed data)")
    print("   - pipe.pkl (trained model pipeline)")
    
    return pipeline, df

if __name__ == "__main__":
    print("🔄 Retraining Laptop Price Predictor Model")
    print("=" * 50)
    
    try:
        pipeline, df = train_model()
        print("\n🎉 Success! You can now run the Streamlit app:")
        print("   streamlit run laptop.py")
        
    except FileNotFoundError:
        print("❌ Error: laptop_data.csv not found!")
        print("Please ensure the dataset file is in the current directory.")
        
    except Exception as e:
        print(f"❌ Error during training: {str(e)}")
        print("Please check your data and try again.")
