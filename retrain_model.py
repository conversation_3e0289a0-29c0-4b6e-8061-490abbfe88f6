#!/usr/bin/env python3
"""
Laptop Price Predictor Model Retraining Script
This script retrains the model exactly as in the original training script to fix compatibility issues.
"""

import pandas as pd
import numpy as np
import pickle
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.metrics import r2_score, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')

def preprocess_data():
    """Load and preprocess the laptop data exactly as in the original script"""
    print("📊 Loading and preprocessing data...")

    # Load the dataset
    df = pd.read_csv('laptop_data.csv')

    # Drop unnecessary columns
    if 'Unnamed: 0' in df.columns:
        df.drop(columns=['Unnamed: 0'], inplace=True)

    # Clean RAM and Weight columns
    df['Ram'] = df['Ram'].str.replace('GB', '').astype('int32')
    df['Weight'] = df['Weight'].str.replace('kg', '').astype('float32')

    # Extract touchscreen and IPS information
    df['Touchscreen'] = df['ScreenResolution'].apply(lambda x: 1 if 'Touchscreen' in x else 0)
    df['Ips'] = df['ScreenResolution'].apply(lambda x: 1 if 'IPS' in x else 0)

    # Extract screen resolution and calculate PPI
    new = df['ScreenResolution'].str.split('x', n=1, expand=True)
    df['X_res'] = new[0]
    df['Y_res'] = new[1]
    df['X_res'] = df['X_res'].str.replace(',', '').str.findall(r'(\d+\.?\d+)').apply(lambda x: x[0])
    df['X_res'] = df['X_res'].astype('int')
    df['Y_res'] = df['Y_res'].astype('int')
    df['ppi'] = (((df['X_res']**2) + (df['Y_res']**2))**0.5 / df['Inches']).astype('float')

    # Drop screen resolution related columns
    df.drop(columns=['ScreenResolution', 'Inches', 'X_res', 'Y_res'], inplace=True)

    # Process CPU
    df['Cpu Name'] = df['Cpu'].apply(lambda x: " ".join(x.split()[0:3]))

    def fetch_processor(text):
        if text == 'Intel Core i7' or text == 'Intel Core i5' or text == 'Intel Core i3':
            return text
        else:
            if text.split()[0] == 'Intel':
                return 'Other Intel Processor'
            else:
                return 'AMD Processor'

    df['Cpu brand'] = df['Cpu Name'].apply(fetch_processor)
    df.drop(columns=['Cpu', 'Cpu Name'], inplace=True)

    # Process Memory (Storage)
    df['Memory'] = df['Memory'].astype(str).replace('\.0', '', regex=True)
    df["Memory"] = df["Memory"].str.replace('GB', '')
    df["Memory"] = df["Memory"].str.replace('TB', '000')
    new = df["Memory"].str.split("+", n=1, expand=True)

    df["first"] = new[0]
    df["first"] = df["first"].str.strip()
    df["second"] = new[1]

    df["Layer1HDD"] = df["first"].apply(lambda x: 1 if "HDD" in x else 0)
    df["Layer1SSD"] = df["first"].apply(lambda x: 1 if "SSD" in x else 0)
    df["Layer1Hybrid"] = df["first"].apply(lambda x: 1 if "Hybrid" in x else 0)
    df["Layer1Flash_Storage"] = df["first"].apply(lambda x: 1 if "Flash Storage" in x else 0)

    df['first'] = df['first'].str.replace(r'\D', '', regex=True)
    df["second"] = df["second"].fillna("0").str.strip()

    df["Layer2HDD"] = df["second"].apply(lambda x: 1 if "HDD" in x else 0)
    df["Layer2SSD"] = df["second"].apply(lambda x: 1 if "SSD" in x else 0)
    df["Layer2Hybrid"] = df["second"].apply(lambda x: 1 if "Hybrid" in x else 0)
    df["Layer2Flash_Storage"] = df["second"].apply(lambda x: 1 if "Flash Storage" in x else 0)

    df['second'] = df['second'].str.replace(r'\D', '', regex=True)
    df["first"] = df["first"].astype(int)
    df["second"] = df["second"].astype(int)

    df["HDD"] = (df["first"] * df["Layer1HDD"] + df["second"] * df["Layer2HDD"])
    df["SSD"] = (df["first"] * df["Layer1SSD"] + df["second"] * df["Layer2SSD"])
    df["Hybrid"] = (df["first"] * df["Layer1Hybrid"] + df["second"] * df["Layer2Hybrid"])
    df["Flash_Storage"] = (df["first"] * df["Layer1Flash_Storage"] + df["second"] * df["Layer2Flash_Storage"])

    df.drop(columns=['first', 'second', 'Layer1HDD', 'Layer1SSD', 'Layer1Hybrid',
                     'Layer1Flash_Storage', 'Layer2HDD', 'Layer2SSD', 'Layer2Hybrid',
                     'Layer2Flash_Storage'], inplace=True)

    df.drop(columns=['Memory'], inplace=True)
    df.drop(columns=['Hybrid', 'Flash_Storage'], inplace=True)

    # Process GPU
    df['Gpu brand'] = df['Gpu'].apply(lambda x: x.split()[0])
    df = df[df['Gpu brand'] != 'ARM']
    df.drop(columns=['Gpu'], inplace=True)

    # Process Operating System
    def cat_os(inp):
        if inp == 'Windows 10' or inp == 'Windows 7' or inp == 'Windows 10 S':
            return 'Windows'
        elif inp == 'macOS' or inp == 'Mac OS X':
            return 'Mac'
        else:
            return 'Others/No OS/Linux'

    df['os'] = df['OpSys'].apply(cat_os)
    df.drop(columns=['OpSys'], inplace=True)

    print(f"✅ Data preprocessing complete. Shape: {df.shape}")
    return df

def create_pipeline():
    """Create the machine learning pipeline"""
    print("🔧 Creating ML pipeline...")
    
    # Use RandomForestRegressor instead of ExtraTreesRegressor for better compatibility
    model = RandomForestRegressor(
        n_estimators=100,
        random_state=42,
        max_depth=15,
        min_samples_split=5,
        min_samples_leaf=2,
        n_jobs=-1
    )
    
    return model

def train_model():
    """Train the laptop price prediction model"""
    print("🚀 Starting model training...")
    
    # Load and preprocess data
    df = preprocess_data()
    
    # Prepare features and target
    X = df.drop('Price', axis=1)
    y = np.log(df['Price'])  # Log transform for better prediction
    
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Create and train the model
    model = create_pipeline()
    
    # Encode categorical variables
    categorical_features = ['Company', 'TypeName', 'Cpu brand', 'Gpu brand', 'os']
    
    # Create label encoders
    label_encoders = {}
    X_train_encoded = X_train.copy()
    X_test_encoded = X_test.copy()
    
    for feature in categorical_features:
        le = LabelEncoder()
        X_train_encoded[feature] = le.fit_transform(X_train[feature])
        X_test_encoded[feature] = le.transform(X_test[feature])
        label_encoders[feature] = le
    
    # Train the model
    print("🎯 Training model...")
    model.fit(X_train_encoded, y_train)
    
    # Make predictions
    y_pred = model.predict(X_test_encoded)
    
    # Calculate metrics
    r2 = r2_score(y_test, y_pred)
    mae = mean_absolute_error(np.exp(y_test), np.exp(y_pred))
    
    print(f"📈 Model Performance:")
    print(f"   R² Score: {r2:.4f}")
    print(f"   Mean Absolute Error: ₹{mae:,.0f}")
    
    # Create pipeline for prediction
    class LaptopPricePipeline:
        def __init__(self, model, label_encoders, feature_columns):
            self.model = model
            self.label_encoders = label_encoders
            self.feature_columns = feature_columns
        
        def predict(self, X):
            X_encoded = X.copy()
            for feature, encoder in self.label_encoders.items():
                if feature in X_encoded.columns:
                    X_encoded[feature] = encoder.transform(X_encoded[feature])
            return self.model.predict(X_encoded)
    
    # Create the pipeline
    pipeline = LaptopPricePipeline(model, label_encoders, X.columns.tolist())
    
    # Save the model and data
    print("💾 Saving model and data...")
    
    # Save the dataframe (for UI dropdowns)
    with open('df.pkl', 'wb') as f:
        pickle.dump(df, f)
    
    # Save the pipeline
    with open('pipe.pkl', 'wb') as f:
        pickle.dump(pipeline, f)
    
    print("✅ Model training complete!")
    print("📁 Files saved:")
    print("   - df.pkl (preprocessed data)")
    print("   - pipe.pkl (trained model pipeline)")
    
    return pipeline, df

if __name__ == "__main__":
    print("🔄 Retraining Laptop Price Predictor Model")
    print("=" * 50)
    
    try:
        pipeline, df = train_model()
        print("\n🎉 Success! You can now run the Streamlit app:")
        print("   streamlit run laptop.py")
        
    except FileNotFoundError:
        print("❌ Error: laptop_data.csv not found!")
        print("Please ensure the dataset file is in the current directory.")
        
    except Exception as e:
        print(f"❌ Error during training: {str(e)}")
        print("Please check your data and try again.")
