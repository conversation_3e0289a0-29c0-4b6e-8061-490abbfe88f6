# -*- coding: utf-8 -*-
"""IMDB reviews- Sentiment Analysis.ipynb

Automatically generated by Colab.

Original file is located at
    https://colab.research.google.com/drive/1sgp6z1ZOeHsxVzbqQHEsYHbxmsRe1er8
"""

!pip install kaggle

import os
import json

from zipfile import ZipFile
import pandas as pd
from sklearn.model_selection import train_test_split
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense,Embedding,LSTM
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.callbacks import EarlyStopping

# data collection
kaggle_dictionary = json.load(open('/content/kaggle.json'))

# setup kaggle credentials as enviroment variables
os.environ['KAGGLE_USERNAME'] = kaggle_dictionary['username']
os.environ['KAGGLE_KEY'] = kaggle_dictionary['key']

!kaggle datasets download -d lakshmi25npathi/imdb-dataset-of-50k-movie-reviews

!ls

with ZipFile('/content/imdb-dataset-of-50k-movie-reviews.zip','r') as zip:
  zip.extractall()

data = pd.read_csv('/content/IMDB Dataset.csv')

data.shape

data.head()

data['sentiment'].value_counts()

# data.replace({'sentiment':{'positive':1,'negative':0}},inplace=true)
data = pd.read_csv('/content/IMDB Dataset.csv')
data.replace({'sentiment':{'positive':1,'negative':0}},inplace=True)

data.head()

train_data,test_data = train_test_split(data,test_size=0.2, random_state=42)

print(train_data.shape)
print(test_data.shape)

# data preprocessing
tokenizer = Tokenizer(num_words=5000)
tokenizer.fit_on_texts(train_data['review'])

x_train = pad_sequences(tokenizer.texts_to_sequences(train_data['review']), maxlen=200)
x_test = pad_sequences(tokenizer.texts_to_sequences(test_data['review']), maxlen=200)

print(x_train)
print(x_test)

y_train = train_data['sentiment']
y_test = test_data['sentiment']

print(y_train)

# buliding the LSTM

model = Sequential()
model.add(Embedding(input_dim=5000, output_dim=128, input_shape=(200,)))

model.add(LSTM(128,dropout=0.2, recurrent_dropout=0.2))
model.add(Dense(1,activation='sigmoid'))

model.summary()

# compile the model
model.compile(loss='binary_crossentropy', optimizer='adam', metrics=['accuracy'])

# training the model
model.fit(x_train, y_train, batch_size=32, epochs=5, validation_data=(x_test, y_test))

# model evaluation
loss, accuracy = model.evaluate(x_test, y_test)
print(f'Test Loss: {loss}, Test Accuracy: {accuracy}')

from collections.abc import Sequence
# buliding the predicating system
def predict_sentiment(review):
  sequence = tokenizer.texts_to_sequences([review])
  padded_sequence = pad_sequences(sequence, maxlen=200)
  prediction = model.predict(padded_sequence)
  sentiment = 'positive' if prediction > 0.5 else 'negative'
  return sentiment

# example
new_review = "this is movie was fantastic.I loved it."
Sentiment = predict_sentiment(new_review)
print(f'Predicted Sentiment: {Sentiment}')

# # to save model
# model.save('sentiment_model.h5')

import pickle

with open('tokenizer.pkl', 'wb') as f:
    pickle.dump(tokenizer, f)

model.save('sentiment_model.keras')

