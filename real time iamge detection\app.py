# -*- coding: utf-8 -*-
"""
Real-time Image Classification Desktop Application
A GUI application for image classification using MobileNetV2
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import tensorflow as tf
from tensorflow.keras.applications.mobilenet_v2 import MobileNetV2, preprocess_input, decode_predictions
from tensorflow.keras.preprocessing import image
import numpy as np
from PIL import Image, ImageTk
import threading
import os

class ImageClassifierApp:
    def __init__(self, root):
        self.root = root
        self.root.title("AI Image Classifier - MobileNetV2")
        self.root.geometry("800x700")
        self.root.configure(bg='#f0f0f0')
        
        # Initialize model
        self.model = None
        self.current_image_path = None
        
        # Create UI
        self.create_widgets()
        
        # Load model in background
        self.load_model()
    
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="AI Image Classifier", 
                               font=('Arial', 20, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Model status
        self.status_var = tk.StringVar()
        self.status_var.set("Loading model...")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                font=('Arial', 10), foreground='blue')
        status_label.grid(row=1, column=0, columnspan=2, pady=(0, 10))
        
        # Image selection frame
        image_frame = ttk.LabelFrame(main_frame, text="Image Selection", padding="10")
        image_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        image_frame.columnconfigure(1, weight=1)
        
        # Browse button
        self.browse_btn = ttk.Button(image_frame, text="Browse Image", 
                                    command=self.browse_image, state='disabled')
        self.browse_btn.grid(row=0, column=0, padx=(0, 10))
        
        # File path display
        self.file_path_var = tk.StringVar()
        self.file_path_var.set("No image selected")
        path_label = ttk.Label(image_frame, textvariable=self.file_path_var, 
                              background='white', relief='sunken', width=50)
        path_label.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # Predict button
        self.predict_btn = ttk.Button(image_frame, text="Classify Image", 
                                     command=self.classify_image, state='disabled')
        self.predict_btn.grid(row=0, column=2, padx=(10, 0))
        
        # Image display frame
        display_frame = ttk.LabelFrame(main_frame, text="Image Preview", padding="10")
        display_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Image label
        self.image_label = ttk.Label(display_frame, text="No image loaded", 
                                    background='white', width=30, anchor='center')
        self.image_label.grid(row=0, column=0, padx=10, pady=10)
        
        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="Classification Results", padding="10")
        results_frame.grid(row=3, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Results text widget with scrollbar
        text_frame = ttk.Frame(results_frame)
        text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.results_text = tk.Text(text_frame, width=40, height=15, 
                                   font=('Courier', 10), wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # Configure text tags for styling (moved here for early setup)
        self.results_text.tag_configure("header", font=('Arial', 10, 'bold'))
        self.results_text.tag_configure("top_pred", font=('Arial', 12, 'bold'), foreground='green')
        self.results_text.tag_configure("top_conf", font=('Arial', 10, 'bold'), foreground='blue')

        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(20, 0))
        
        # Configure main_frame row weights
        main_frame.rowconfigure(3, weight=1)
    
    def load_model(self):
        """Load the MobileNetV2 model in a separate thread"""
        def load():
            try:
                self.status_var.set("Loading MobileNetV2 model...")
                self.progress.start()
                
                # Load pre-trained MobileNetV2 model
                self.model = MobileNetV2(weights='imagenet')
                
                self.progress.stop()
                self.status_var.set("Model loaded successfully! Ready to classify images.")
                
                # Enable buttons
                self.browse_btn.configure(state='normal')
                
            except Exception as e:
                self.progress.stop()
                self.status_var.set(f"Error loading model: {str(e)}")
                messagebox.showerror("Error", f"Failed to load model:\n{str(e)}")
        
        # Start loading in background thread
        threading.Thread(target=load, daemon=True).start()
    
    def browse_image(self):
        """Open file dialog to select an image"""
        file_types = [
            ('Image files', '*.jpg *.jpeg *.png *.bmp *.gif *.tiff'),
            ('JPEG files', '*.jpg *.jpeg'),
            ('PNG files', '*.png'),
            ('All files', '*.*')
        ]
        
        filename = filedialog.askopenfilename(
            title="Select an image file",
            filetypes=file_types
        )
        
        if filename:
            self.current_image_path = filename
            self.file_path_var.set(os.path.basename(filename))
            self.display_image(filename)
            self.predict_btn.configure(state='normal')
            
            # Clear previous results
            self.results_text.delete(1.0, tk.END)
    
    def display_image(self, img_path):
        """Display the selected image in the preview area"""
        try:
            # Open and resize image for display
            img = Image.open(img_path)
            
            # Calculate size to fit in display area (max 250x250)
            img.thumbnail((250, 250), Image.Resampling.LANCZOS)
            
            # Convert to PhotoImage for tkinter
            photo = ImageTk.PhotoImage(img)
            
            # Update label
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # Keep a reference
            
        except Exception as e:
            self.image_label.configure(text=f"Error loading image:\n{str(e)}", image="")
            messagebox.showerror("Error", f"Failed to load image:\n{str(e)}")
    
    def classify_image(self):
        """Classify the selected image"""
        if not self.current_image_path or not self.model:
            messagebox.showwarning("Warning", "Please select an image and ensure model is loaded.")
            return
        
        def classify():
            try:
                self.progress.start()
                self.status_var.set("Classifying image...")
                self.predict_btn.configure(state='disabled')
                
                # Load and preprocess the image
                img = image.load_img(self.current_image_path, target_size=(224, 224))
                img_array = image.img_to_array(img)
                img_array = np.expand_dims(img_array, axis=0)
                img_array = preprocess_input(img_array)
                
                # Make prediction
                predictions = self.model.predict(img_array, verbose=0)
                
                # Decode predictions
                decoded_predictions = decode_predictions(predictions, top=5)[0]
                
                # Update results
                self.update_results(decoded_predictions)
                
                self.progress.stop()
                self.status_var.set("Classification complete!")
                self.predict_btn.configure(state='normal')
                
            except Exception as e:
                self.progress.stop()
                self.status_var.set(f"Error during classification: {str(e)}")
                self.predict_btn.configure(state='normal')
                messagebox.showerror("Error", f"Classification failed:\n{str(e)}")
        
        # Run classification in background thread
        threading.Thread(target=classify, daemon=True).start()
    
    def update_results(self, predictions):
        """Update the results text widget with predictions"""
        self.results_text.delete(1.0, tk.END)
        
        # Add header
        self.results_text.insert(tk.END, "🔍 Classification Results\n")
        self.results_text.insert(tk.END, "=" * 35 + "\n\n")
        
        # Add predictions
        for i, (imagenet_id, label, score) in enumerate(predictions):
            confidence = score * 100
            
            # Format the result
            result_line = f"{i+1}. {label.replace('_', ' ').title()}\n"
            confidence_line = f"   Confidence: {confidence:.2f}%\n"
            id_line = f"   ID: {imagenet_id}\n\n"
            
            # Add with different formatting for top prediction
            if i == 0:
                self.results_text.insert(tk.END, "🏆 TOP PREDICTION:\n", "header")
                self.results_text.insert(tk.END, result_line, "top_pred")
                self.results_text.insert(tk.END, confidence_line, "top_conf")
                self.results_text.insert(tk.END, id_line)
                self.results_text.insert(tk.END, "Other possibilities:\n\n")
            else:
                self.results_text.insert(tk.END, result_line)
                self.results_text.insert(tk.END, confidence_line)
                self.results_text.insert(tk.END, id_line)
        


def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = ImageClassifierApp(root)
    
    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    # Set minimum window size
    root.minsize(800, 600)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\nApplication closed by user")


if __name__ == "__main__":
    main()